# connectors/chatbot_memory_connector.py

"""
Enhanced Chatbot-Memory Connector for Adrina AI Assistant

This connector integrates the chatbot with the memory system,
providing context-aware conversations with memory capabilities.
"""

import sys
import os
import logging
import threading
from queue import Queue
from typing import Optional, Dict, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chatbot.chatbot import Chatbot
from connectors.memory_connector import MemoryConnector

class ChatbotMemoryConnector:
    """
    Enhanced connector that combines chatbot functionality with memory system.
    Provides context-aware conversations and automatic memory storage.
    """
    
    def __init__(self,
                 model_path: str,
                 memory_db_path: str = "./memory/memory_db",
                 enable_memory: bool = True,
                 context_window: int = 3):
        """
        Initialize the enhanced chatbot with memory.
        
        Args:
            model_path: Path to the Llama model file
            memory_db_path: Path for memory database storage
            enable_memory: Whether to enable memory functionality
            context_window: Number of relevant memories to include in context
        """
        self.logger = logging.getLogger(__name__)
        
        # Initialize the base chatbot
        self.chatbot = Chatbot(model_path)
        
        # Initialize memory connector
        self.memory_connector = MemoryConnector(
            memory_db_path=memory_db_path,
            enable_memory=enable_memory,
            context_window=context_window
        )
        
        self.logger.info("Enhanced Chatbot-Memory Connector initialized")
    
    def start_session(self, session_id: Optional[str] = None) -> str:
        """Start a new conversation session."""
        return self.memory_connector.start_session(session_id)
    
    def generate_response(self, user_input: str, include_context: bool = True) -> str:
        """
        Generate a response with memory-enhanced context.
        
        Args:
            user_input: User's input message
            include_context: Whether to include memory context
            
        Returns:
            AI response string
        """
        try:
            # Get memory context if enabled
            context = ""
            if include_context and self.memory_connector.is_memory_enabled():
                context = self.memory_connector.get_conversation_context(user_input)
            
            # Prepare enhanced prompt
            enhanced_input = self._prepare_enhanced_prompt(user_input, context)
            
            # Generate response using base chatbot
            response = self.chatbot.generate_response(enhanced_input)
            
            # Store conversation in memory
            if self.memory_connector.is_memory_enabled():
                self.memory_connector.store_conversation(user_input, response)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            # Fallback to base chatbot without memory
            return self.chatbot.generate_response(user_input)
    
    def generate_response_stream(self, user_input: str, include_context: bool = True):
        """
        Generate a streaming response with memory-enhanced context.
        
        Args:
            user_input: User's input message
            include_context: Whether to include memory context
            
        Yields:
            Response chunks
        """
        try:
            # Get memory context if enabled
            context = ""
            if include_context and self.memory_connector.is_memory_enabled():
                context = self.memory_connector.get_conversation_context(user_input)
            
            # Prepare enhanced prompt
            enhanced_input = self._prepare_enhanced_prompt(user_input, context)
            
            # Generate streaming response
            full_response = ""
            for chunk in self.chatbot.generate_response_stream(enhanced_input):
                full_response += chunk
                yield chunk
            
            # Store conversation in memory after streaming is complete
            if self.memory_connector.is_memory_enabled():
                self.memory_connector.store_conversation(user_input, full_response)
            
        except Exception as e:
            self.logger.error(f"Error generating streaming response: {e}")
            # Fallback to base chatbot without memory
            for chunk in self.chatbot.generate_response_stream(user_input):
                yield chunk
    
    def generate_response_with_threading(self, user_input: str, sentence_queue: Queue, include_context: bool = True):
        """
        Generate response using threading (compatible with existing TTS connector).
        
        Args:
            user_input: User's input message
            sentence_queue: Queue for sentence output
            include_context: Whether to include memory context
        """
        try:
            # Get memory context if enabled
            context = ""
            if include_context and self.memory_connector.is_memory_enabled():
                context = self.memory_connector.get_conversation_context(user_input)
            
            # Prepare enhanced prompt
            enhanced_input = self._prepare_enhanced_prompt(user_input, context)
            
            # Generate streaming response and queue sentences
            full_response = ""
            current_sentence = ""
            
            for chunk in self.chatbot.generate_response_stream(enhanced_input):
                full_response += chunk
                current_sentence += chunk
                
                # Check for sentence boundaries
                if any(punct in chunk for punct in ['.', '!', '?', '\n']):
                    if current_sentence.strip():
                        sentence_queue.put(current_sentence.strip())
                        current_sentence = ""
            
            # Add any remaining text
            if current_sentence.strip():
                sentence_queue.put(current_sentence.strip())
            
            # Signal end of generation
            sentence_queue.put(None)
            
            # Store conversation in memory
            if self.memory_connector.is_memory_enabled():
                self.memory_connector.store_conversation(user_input, full_response)
            
        except Exception as e:
            self.logger.error(f"Error in threaded generation: {e}")
            sentence_queue.put(None)  # Signal end even on error
    
    def store_fact(self, fact: str, category: str = "general", importance: int = 5) -> bool:
        """Store a fact in memory."""
        return self.memory_connector.store_fact(fact, category, importance)
    
    def store_preference(self, preference: str, user_id: str = "default") -> bool:
        """Store a user preference in memory."""
        return self.memory_connector.store_preference(preference, user_id)
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics."""
        return self.memory_connector.get_memory_stats()
    
    def search_memories(self, query: str, memory_types: Optional[list] = None, max_results: int = 5) -> list:
        """Search for memories matching a query."""
        return self.memory_connector.search_memories(query, memory_types, max_results)
    
    def is_memory_enabled(self) -> bool:
        """Check if memory system is enabled and working."""
        return self.memory_connector.is_memory_enabled()
    
    def disable_memory(self):
        """Temporarily disable memory functionality."""
        self.memory_connector.disable_memory()
    
    def enable_memory_system(self):
        """Re-enable memory functionality."""
        self.memory_connector.enable_memory_system()
    
    def clear_session(self):
        """Clear the current session."""
        self.memory_connector.clear_session()
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the chatbot and memory system."""
        memory_status = self.memory_connector.get_status()
        return {
            "chatbot_loaded": self.chatbot is not None,
            "memory_status": memory_status
        }
    
    def _prepare_enhanced_prompt(self, user_input: str, context: str) -> str:
        """Prepare an enhanced prompt with memory context."""
        if not context:
            return user_input

        # Extract personal information from context for better handling
        personal_info = self._extract_personal_info(context)

        # Create a memory-driven prompt that lets the AI use its stored knowledge
        enhanced_prompt = f"""Based on your memory and the following context, respond to the user:

RELEVANT MEMORIES:
{context}

{personal_info}

USER: {user_input}"""

        return enhanced_prompt

    def _extract_personal_info(self, context: str) -> str:
        """Extract and format personal information from context"""
        personal_info = ""

        # Look for name information in the context
        if "name is" in context.lower():
            # Extract name patterns
            import re
            name_patterns = [
                r"my name is ([A-Za-z\s]+)",
                r"i am ([A-Za-z\s]+)",
                r"call me ([A-Za-z\s]+)",
                r"name is ([A-Za-z\s]+)",
                r"user name is ([A-Za-z\s]+)",
                r"user's name is ([A-Za-z\s]+)",
                r"the user name is ([A-Za-z\s]+)",
                r"the user's name is ([A-Za-z\s]+)"
            ]

            for pattern in name_patterns:
                matches = re.findall(pattern, context, re.IGNORECASE)
                if matches:
                    # Get the most recent name mention
                    name = matches[-1].strip()
                    if name and name.lower() not in ['adrina', 'assistant', 'ai', 'human', 'user']:
                        personal_info += f"IDENTITY CONTEXT:\n"
                        personal_info += f"- The user's name is: {name}\n"
                        personal_info += f"- You are Adrina, the AI assistant\n"
                        personal_info += f"- Do not confuse your identity with the user's identity\n"
                        break

        # Look for other personal information
        if "creator" in context.lower():
            personal_info += "IMPORTANT: This user is your creator.\n"

        return personal_info


# Enhanced GeneratorThread for use with TTS connector
class EnhancedGeneratorThread(threading.Thread):
    """Enhanced generator thread that includes memory functionality."""
    
    def __init__(self, chatbot_memory_connector, user_input, sentence_queue):
        super().__init__()
        self.chatbot_memory_connector = chatbot_memory_connector
        self.user_input = user_input
        self.sentence_queue = sentence_queue
        self._stop_event = threading.Event()
    
    def run(self):
        """Run the enhanced generation with memory context."""
        self.chatbot_memory_connector.generate_response_with_threading(
            self.user_input, 
            self.sentence_queue
        )
    
    def stop(self):
        """Stop the generation thread."""
        self._stop_event.set()


# Example usage and testing
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Test the enhanced connector
    print("🧪 Testing Enhanced Chatbot-Memory Connector")
    print("=" * 50)
    
    try:
        model_path = "models/Lexi-Llama-3-8B-Uncensored-Q8_0.gguf"
        
        if not os.path.exists(model_path):
            print(f"❌ Model file not found: {model_path}")
            print("Please check the model path")
            exit(1)
        
        # Initialize enhanced connector
        enhanced_connector = ChatbotMemoryConnector(model_path)
        
        # Check status
        status = enhanced_connector.get_status()
        print(f"System Status: {status}")
        
        # Start a session
        session_id = enhanced_connector.start_session()
        print(f"Started session: {session_id}")
        
        # Test conversation with memory
        user_input = "Hello, I'm interested in learning Python programming."
        print(f"\nUser: {user_input}")
        
        response = enhanced_connector.generate_response(user_input)
        print(f"Adrina: {response}")
        
        # Test memory stats
        stats = enhanced_connector.get_memory_stats()
        print(f"\nMemory Stats: {stats}")
        
        print("\n🎉 Enhanced connector test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("Make sure Ollama is running and the model path is correct")
