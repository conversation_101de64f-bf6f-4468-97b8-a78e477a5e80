#!/usr/bin/env python3
"""
Offline Connector for Adrina AI Assistant v2.0

100% offline operation using:
- Local GGUF models for AI processing
- Local nomic embeddings for memory
- Local TTS for voice output
- No internet or network dependencies
"""

import os
import sys
import time
from typing import Dict

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from chatbot.tiny_llm_classifier import TinyLLMClassifier, QueryCategory
from connectors.chatbot_memory_connector import ChatbotMemoryConnector
from memory.offline_memory_manager import OfflineMemoryManager
from config.main_chatbot_config import LLAMA_MODEL_PATH
from universal_logging.universal_logger import ComponentType, get_logger

class OfflineConnector:
    """
    100% offline connector for Adrina AI Assistant.
    
    Provides complete functionality without any network dependencies:
    - Local AI models for processing
    - Local embeddings for memory
    - Local TTS for voice output
    """
    
    def __init__(self):
        self.logger = get_logger()
        
        # Core components
        self.tiny_classifier = None
        self.chatbot = None
        self.offline_memory = None
        
        # Statistics
        self.stats = {
            'total_queries': 0,
            'simple_queries': 0,
            'complex_queries': 0,
            'research_queries': 0,
            'memory_queries': 0,
            'avg_classification_time': 0.0,
            'avg_response_time': 0.0
        }
        
        # Initialize system
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize all offline components."""
        try:
            self.logger.log_system_event("🚀 Initializing 100% Offline System...")
            
            # Initialize tiny LLM classifier
            self.logger.log_system_event("🧠 Loading tiny LLM classifier...")
            self.tiny_classifier = TinyLLMClassifier()
            
            # Initialize main chatbot (without memory for now)
            self.logger.log_system_event("💬 Loading main chatbot...")
            self.chatbot = ChatbotMemoryConnector(LLAMA_MODEL_PATH)
            
            # Initialize offline memory system
            self.logger.log_system_event("💾 Loading offline memory system...")
            self.offline_memory = OfflineMemoryManager()
            
            # Check system status
            classifier_ok = self.tiny_classifier is not None
            chatbot_ok = self.chatbot is not None
            memory_ok = self.offline_memory.is_available
            
            self.logger.log_system_event(f"📊 System Status:")
            self.logger.log_system_event(f"  - Classifier: {'✅' if classifier_ok else '❌'}")
            self.logger.log_system_event(f"  - Chatbot: {'✅' if chatbot_ok else '❌'}")
            self.logger.log_system_event(f"  - Memory: {'✅' if memory_ok else '❌'}")
            
            if classifier_ok and chatbot_ok:
                self.logger.log_system_event("✅ Offline System ready!")
                if not memory_ok:
                    self.logger.log_system_event("⚠️ Memory system unavailable - download nomic model for full functionality")
            else:
                self.logger.log_system_event("❌ Offline System initialization failed")
            
        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "offline system initialization")
            raise
    
    def process_and_speak_offline(self, user_input: str) -> Dict:
        """
        Process user input with 100% offline operation.
        
        Args:
            user_input: User's query
            
        Returns:
            Dictionary with response and processing information
        """
        try:
            start_time = time.time()
            self.stats['total_queries'] += 1
            
            # Step 1: Ultra-fast classification
            self.logger.log_system_event(f"🔍 Classifying: '{user_input[:50]}...'")
            classification_start = time.time()
            
            classification = self.tiny_classifier.classify_query(user_input)
            classification_time = time.time() - classification_start
            
            self.logger.log_system_event(
                f"📊 Result: {classification.category.value} "
                f"(confidence: {classification.confidence:.2f}, "
                f"time: {classification.processing_time:.3f}s)"
            )
            
            # Step 2: Route based on classification
            response_start = time.time()
            
            if classification.category == QueryCategory.SIMPLE:
                response = self._handle_simple_query(user_input)
                self.stats['simple_queries'] += 1
                
            elif classification.category == QueryCategory.COMPLEX:
                response = self._handle_complex_query(user_input)
                self.stats['complex_queries'] += 1
                
            elif classification.category == QueryCategory.RESEARCH:
                response = self._handle_research_query(user_input)
                self.stats['research_queries'] += 1
                
            else:  # UNKNOWN
                response = self._handle_simple_query(user_input)
                self.stats['simple_queries'] += 1
            
            response_time = time.time() - response_start
            total_time = time.time() - start_time
            
            # Store conversation in offline memory
            if self.offline_memory.is_available:
                self.offline_memory.store_conversation(user_input, response)
            
            # Update statistics
            self._update_stats(classification_time, total_time)
            
            # Prepare response info
            processing_info = {
                'query_type': classification.category.value,
                'classification_confidence': classification.confidence,
                'classification_time': classification_time,
                'response_time': response_time,
                'total_time': total_time,
                'processing_time': response_time,
                'routing_method': 'offline_local',
                'memory_available': self.offline_memory.is_available
            }
            
            return {
                'response': response,
                'clean_response': response,
                'processing_info': processing_info,
                'classification': classification
            }
            
        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "offline processing")
            error_response = f"I encountered an error: {e}"
            
            return {
                'response': error_response,
                'clean_response': error_response,
                'processing_info': {
                    'query_type': 'ERROR',
                    'total_time': 0.0,
                    'classification_time': 0.0,
                    'processing_time': 0.0,
                    'error': str(e)
                },
                'classification': None
            }
    
    def _handle_simple_query(self, user_input: str) -> str:
        """Handle simple queries with main chatbot only."""
        self.logger.log_system_event("⚡ Processing SIMPLE query with main chatbot")
        
        try:
            response = self.chatbot.chatbot.generate_response(user_input)
            return response
        except Exception as e:
            self.logger.log_error(ComponentType.MAIN_CHATBOT, e, "simple query processing")
            return f"Sorry, I had trouble processing that request: {e}"
    
    def _handle_complex_query(self, user_input: str) -> str:
        """Handle complex queries with memory context if available."""
        self.logger.log_system_event("🔧 Processing COMPLEX query")
        
        try:
            # Check if we should use memory context
            if self.offline_memory.is_available and self._needs_memory_context(user_input):
                return self._handle_memory_query(user_input)
            else:
                # Direct response without memory
                response = self.chatbot.chatbot.generate_response(user_input)
                return response
                
        except Exception as e:
            self.logger.log_error(ComponentType.MAIN_CHATBOT, e, "complex query processing")
            return f"Sorry, I had trouble processing that complex request: {e}"
    
    def _handle_research_query(self, user_input: str) -> str:
        """Handle research queries with detailed responses."""
        self.logger.log_system_event("🔬 Processing RESEARCH query")
        
        try:
            # For research queries, always try to use memory context if available
            if self.offline_memory.is_available:
                return self._handle_memory_query(user_input)
            else:
                # Generate detailed response without memory
                enhanced_prompt = f"Please provide a detailed, comprehensive explanation for: {user_input}"
                response = self.chatbot.chatbot.generate_response(enhanced_prompt)
                return response
                
        except Exception as e:
            self.logger.log_error(ComponentType.MAIN_CHATBOT, e, "research query processing")
            return f"Sorry, I had trouble researching that topic: {e}"
    
    def _handle_memory_query(self, user_input: str) -> str:
        """Handle queries that benefit from memory context."""
        self.logger.log_system_event("💾 Processing query with offline memory context")
        self.stats['memory_queries'] += 1
        
        try:
            # Get relevant context from offline memory
            context = self.offline_memory.get_context_for_query(user_input)
            
            if context:
                # Create enhanced prompt with context
                enhanced_prompt = f"""Based on our previous conversations and what I know:

{context}

User question: {user_input}

Please provide a helpful response using the above context when relevant."""
                
                response = self.chatbot.chatbot.generate_response(enhanced_prompt)
                self.logger.log_system_event("✅ Generated response with memory context")
                return response
            else:
                # No relevant context found, use direct response
                response = self.chatbot.chatbot.generate_response(user_input)
                return response
                
        except Exception as e:
            self.logger.log_error(ComponentType.MEMORY_SYSTEM, e, "memory query processing")
            # Fallback to direct response
            return self.chatbot.chatbot.generate_response(user_input)
    
    def _needs_memory_context(self, user_input: str) -> bool:
        """Determine if a query would benefit from memory context."""
        memory_indicators = [
            'remember', 'recall', 'what did', 'before', 'earlier',
            'my name', 'about me', 'i told you', 'we discussed',
            'last time', 'previous', 'you know', 'as i mentioned'
        ]
        
        user_lower = user_input.lower()
        return any(indicator in user_lower for indicator in memory_indicators)
    
    def _update_stats(self, classification_time: float, total_time: float):
        """Update processing statistics."""
        total = self.stats['total_queries']
        
        # Update classification time average
        current_class_avg = self.stats['avg_classification_time']
        self.stats['avg_classification_time'] = ((current_class_avg * (total - 1)) + classification_time) / total
        
        # Update response time average
        current_resp_avg = self.stats['avg_response_time']
        self.stats['avg_response_time'] = ((current_resp_avg * (total - 1)) + total_time) / total
    
    def get_stats(self) -> Dict:
        """Get connector statistics."""
        memory_stats = {}
        if self.offline_memory and self.offline_memory.is_available:
            memory_stats = self.offline_memory.get_memory_stats()
        
        return {
            'total_queries': self.stats['total_queries'],
            'simple_queries': self.stats['simple_queries'],
            'complex_queries': self.stats['complex_queries'],
            'research_queries': self.stats['research_queries'],
            'memory_queries': self.stats['memory_queries'],
            'avg_classification_time': self.stats['avg_classification_time'],
            'avg_response_time': self.stats['avg_response_time'],
            'tiny_classifier_stats': self.tiny_classifier.get_stats() if self.tiny_classifier else {},
            'offline_memory_available': self.offline_memory.is_available if self.offline_memory else False,
            'memory_stats': memory_stats,
            'system_mode': '100% OFFLINE'
        }
    
    def shutdown(self):
        """Shutdown the offline connector."""
        self.logger.log_system_event("🔄 Shutting down Offline System...")
        self.logger.log_system_event("✅ Offline System shutdown complete")

# Test function
def test_offline_connector():
    """Test the offline connector."""
    print("🧪 Testing Offline Connector")
    print("=" * 50)
    
    connector = OfflineConnector()
    
    test_queries = [
        "hi there",
        "what's your name?",
        "create a secure authentication system",
        "explain quantum computing in detail",
        "remember that I like Python programming"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        result = connector.process_and_speak_offline(query)
        
        print(f"📊 Category: {result['processing_info']['query_type']}")
        print(f"⏱️ Time: {result['processing_info']['total_time']:.3f}s")
        print(f"💬 Response: {result['response'][:100]}...")
    
    print("\n📊 Final Statistics:")
    stats = connector.get_stats()
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    
    connector.shutdown()

if __name__ == "__main__":
    test_offline_connector()
