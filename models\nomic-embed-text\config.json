{"activation_function": "swig<PERSON>", "architectures": ["NomicBertModel"], "attn_pdrop": 0.0, "auto_map": {"AutoConfig": "configuration_hf_nomic_bert.NomicBertConfig", "AutoModel": "modeling_hf_nomic_bert.NomicBertModel", "AutoModelForMaskedLM": "modeling_hf_nomic_bert.NomicBertForPreTraining"}, "bos_token_id": null, "causal": false, "dense_seq_output": true, "embd_pdrop": 0.0, "eos_token_id": null, "fused_bias_fc": true, "fused_dropout_add_ln": true, "initializer_range": 0.02, "layer_norm_epsilon": 1e-12, "mlp_fc1_bias": false, "mlp_fc2_bias": false, "model_type": "nomic_bert", "n_embd": 768, "n_head": 12, "n_inner": 3072, "n_layer": 12, "n_positions": 8192, "pad_vocab_size_multiple": 64, "parallel_block": false, "parallel_block_tied_norm": false, "prenorm": false, "qkv_proj_bias": false, "reorder_and_upcast_attn": false, "resid_pdrop": 0.0, "rotary_emb_base": 1000, "rotary_emb_fraction": 1.0, "rotary_emb_interleaved": false, "rotary_emb_scale_base": null, "rotary_scaling_factor": 2, "scale_attn_by_inverse_layer_idx": false, "scale_attn_weights": true, "summary_activation": null, "summary_first_dropout": 0.1, "summary_proj_to_labels": true, "summary_type": "cls_index", "summary_use_proj": true, "torch_dtype": "float32", "transformers_version": "4.34.0", "type_vocab_size": 2, "use_cache": true, "use_flash_attn": true, "use_rms_norm": false, "use_xentropy": true, "vocab_size": 30528}