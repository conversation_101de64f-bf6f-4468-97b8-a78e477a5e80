# chroma_storage.py

import chromadb
from chromadb.config import Settings
from typing import List, Dict, Any, Optional, Tuple
import logging
import uuid
from datetime import datetime
import json
import os

class ChromaDBStorage:
    """ChromaDB integration for vector storage and retrieval of memories."""
    
    def __init__(self,
                 persist_directory: str = "./memory/memory_db",
                 collection_name: str = "adrina_memories"):
        """
        Initialize ChromaDB storage.
        
        Args:
            persist_directory: Directory to persist the database
            collection_name: Name of the ChromaDB collection
        """
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        self.logger = logging.getLogger(__name__)
        
        # Ensure the persist directory exists
        os.makedirs(persist_directory, exist_ok=True)
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Get or create collection
        self.collection = self._get_or_create_collection()
        
        self.logger.info(f"ChromaDB initialized with collection '{collection_name}' at '{persist_directory}'")
    
    def _get_or_create_collection(self):
        """Get existing collection or create a new one."""
        try:
            # Try to get existing collection
            collection = self.client.get_collection(name=self.collection_name)
            self.logger.info(f"Loaded existing collection '{self.collection_name}' with {collection.count()} items")
            return collection
        except Exception:
            # Create new collection if it doesn't exist
            collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "Adrina AI Assistant Memory Storage"}
            )
            self.logger.info(f"Created new collection '{self.collection_name}'")
            return collection
    
    def add_memory(self, 
                   text: str, 
                   embedding: List[float], 
                   memory_type: str = "conversation",
                   metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Add a memory to the database.
        
        Args:
            text: The text content of the memory
            embedding: Vector embedding of the text
            memory_type: Type of memory (conversation, fact, preference, etc.)
            metadata: Additional metadata for the memory
            
        Returns:
            The ID of the stored memory
        """
        memory_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        # Prepare metadata
        memory_metadata = {
            "memory_type": memory_type,
            "timestamp": timestamp,
            "text_length": len(text)
        }
        
        if metadata:
            memory_metadata.update(metadata)
        
        try:
            self.collection.add(
                ids=[memory_id],
                embeddings=[embedding],
                documents=[text],
                metadatas=[memory_metadata]
            )
            
            self.logger.debug(f"Added memory {memory_id} of type '{memory_type}'")
            return memory_id
            
        except Exception as e:
            self.logger.error(f"Failed to add memory: {e}")
            raise
    
    def search_memories(self, 
                       query_embedding: List[float], 
                       n_results: int = 5,
                       memory_types: Optional[List[str]] = None,
                       time_filter: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:
        """
        Search for similar memories.
        
        Args:
            query_embedding: Vector embedding of the query
            n_results: Number of results to return
            memory_types: Filter by memory types (optional)
            time_filter: Time-based filter (optional)
            
        Returns:
            List of memory dictionaries with text, metadata, and similarity scores
        """
        try:
            # Build where clause for filtering - simplified for ChromaDB compatibility
            where_clause = None

            # For now, we'll handle filtering after the query to avoid ChromaDB compatibility issues
            # This is less efficient but more reliable across ChromaDB versions

            # Perform similarity search without complex where clauses
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results * 2,  # Get more results to filter later
                include=["documents", "metadatas", "distances"]
            )
            
            # Format results and apply post-query filtering
            memories = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    metadata = results["metadatas"][0][i]
                    distance = results["distances"][0][i]

                    # Apply memory type filter
                    if memory_types and metadata.get("memory_type") not in memory_types:
                        continue

                    # Apply time filter
                    if time_filter:
                        timestamp = metadata.get("timestamp")
                        if timestamp:
                            from datetime import datetime
                            try:
                                mem_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

                                if "after" in time_filter:
                                    after_time = datetime.fromisoformat(time_filter["after"].replace('Z', '+00:00'))
                                    if mem_time < after_time:
                                        continue

                                if "before" in time_filter:
                                    before_time = datetime.fromisoformat(time_filter["before"].replace('Z', '+00:00'))
                                    if mem_time > before_time:
                                        continue
                            except (ValueError, TypeError):
                                # Skip if timestamp parsing fails
                                continue

                    # Convert distance to similarity score (0-1 range)
                    # For cosine distance: similarity = 1 - distance
                    # For L2 distance: similarity = 1 / (1 + distance)
                    # We'll use a more robust conversion that works for both
                    similarity_score = max(0, 1 - distance) if distance <= 2 else 1 / (1 + distance)

                    memory = {
                        "id": results["ids"][0][i],
                        "text": doc,
                        "metadata": metadata,
                        "similarity_score": similarity_score,
                        "distance": distance
                    }
                    memories.append(memory)

                    # Stop when we have enough results
                    if len(memories) >= n_results:
                        break
            
            self.logger.debug(f"Found {len(memories)} similar memories")
            return memories
            
        except Exception as e:
            self.logger.error(f"Failed to search memories: {e}")
            return []
    
    def get_memory_by_id(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific memory by its ID."""
        try:
            results = self.collection.get(
                ids=[memory_id],
                include=["documents", "metadatas"]
            )
            
            if results["documents"] and results["documents"][0]:
                return {
                    "id": memory_id,
                    "text": results["documents"][0],
                    "metadata": results["metadatas"][0]
                }
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get memory {memory_id}: {e}")
            return None
    
    def update_memory(self, memory_id: str, 
                     text: Optional[str] = None,
                     embedding: Optional[List[float]] = None,
                     metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Update an existing memory."""
        try:
            update_data = {"ids": [memory_id]}
            
            if text is not None:
                update_data["documents"] = [text]
            
            if embedding is not None:
                update_data["embeddings"] = [embedding]
            
            if metadata is not None:
                # Get existing metadata and update it
                existing = self.get_memory_by_id(memory_id)
                if existing:
                    updated_metadata = existing["metadata"].copy()
                    updated_metadata.update(metadata)
                    updated_metadata["last_updated"] = datetime.now().isoformat()
                    update_data["metadatas"] = [updated_metadata]
            
            self.collection.update(**update_data)
            self.logger.debug(f"Updated memory {memory_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update memory {memory_id}: {e}")
            return False
    
    def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory by its ID."""
        try:
            self.collection.delete(ids=[memory_id])
            self.logger.debug(f"Deleted memory {memory_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to delete memory {memory_id}: {e}")
            return False
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get statistics about stored memories."""
        try:
            total_count = self.collection.count()
            
            # Get all metadata to analyze
            all_data = self.collection.get(include=["metadatas"])
            
            stats = {
                "total_memories": total_count,
                "memory_types": {},
                "oldest_memory": None,
                "newest_memory": None
            }
            
            if all_data["metadatas"]:
                timestamps = []
                for metadata in all_data["metadatas"]:
                    # Count memory types
                    memory_type = metadata.get("memory_type", "unknown")
                    stats["memory_types"][memory_type] = stats["memory_types"].get(memory_type, 0) + 1
                    
                    # Track timestamps
                    if "timestamp" in metadata:
                        timestamps.append(metadata["timestamp"])
                
                if timestamps:
                    timestamps.sort()
                    stats["oldest_memory"] = timestamps[0]
                    stats["newest_memory"] = timestamps[-1]
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get memory stats: {e}")
            return {"error": str(e)}
    
    def clear_all_memories(self) -> bool:
        """Clear all memories from the collection. Use with caution!"""
        try:
            # Delete the collection and recreate it
            self.client.delete_collection(name=self.collection_name)
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "Adrina AI Assistant Memory Storage"}
            )
            self.logger.warning("All memories have been cleared!")
            return True
        except Exception as e:
            self.logger.error(f"Failed to clear memories: {e}")
            return False


# Testing code
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # Test ChromaDB storage
    storage = ChromaDBStorage()
    
    # Test adding a memory (you would normally get this from the embedding service)
    test_embedding = [0.1] * 384  # Dummy embedding for testing
    
    memory_id = storage.add_memory(
        text="User asked about the weather today",
        embedding=test_embedding,
        memory_type="conversation",
        metadata={"user": "test_user", "topic": "weather"}
    )
    
    print(f"Added memory with ID: {memory_id}")
    
    # Test searching
    results = storage.search_memories(test_embedding, n_results=3)
    print(f"Found {len(results)} similar memories")
    
    # Test stats
    stats = storage.get_memory_stats()
    print(f"Memory stats: {stats}")
