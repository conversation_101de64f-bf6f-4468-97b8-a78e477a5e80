#!/usr/bin/env python3
"""
Error Notification UI for Adrina AI Assistant v2.0

Handles real-time error notifications to users with:
- Immediate error alerts during conversation
- Detailed error descriptions and solutions
- User-friendly error formatting
- Error acknowledgment and cleanup
"""

import os
import sys
from typing import List, Optional, Dict, Any

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from universal_logging.error_alert_system import (
    get_error_alert_system, get_pending_error_alerts, 
    mark_error_notified, get_error_summary, ErrorAlert, ErrorSeverity
)

class ErrorNotificationHandler:
    """Handles user notifications for system errors."""
    
    def __init__(self):
        self.error_alert_system = get_error_alert_system()
        self.notification_count = 0
    
    def check_and_display_errors(self) -> bool:
        """
        Check for pending errors and display them to user.
        
        Returns:
            True if errors were displayed, False otherwise
        """
        try:
            pending_alerts = get_pending_error_alerts(max_alerts=3)
            
            if not pending_alerts:
                return False
            
            self._display_error_alerts(pending_alerts)
            
            # Mark all displayed errors as notified
            for alert in pending_alerts:
                mark_error_notified(alert.error_id)
            
            self.notification_count += len(pending_alerts)
            return True
            
        except Exception as e:
            print(f"⚠️ Error in notification system: {e}")
            return False
    
    def _display_error_alerts(self, alerts: List[ErrorAlert]):
        """Display error alerts to the user."""
        print("\n" + "🚨" * 25)
        print("🚨 SYSTEM ALERT - Issues Detected 🚨")
        print("🚨" * 25)
        
        for i, alert in enumerate(alerts, 1):
            self._display_single_alert(alert, i, len(alerts))
        
        print("🚨" * 25)
        print("💡 These issues have been logged and the system will continue operating.")
        print("   Type 'errors' to see detailed error information anytime.")
        print("🚨" * 25 + "\n")
    
    def _display_single_alert(self, alert: ErrorAlert, index: int, total: int):
        """Display a single error alert."""
        severity_icons = {
            ErrorSeverity.CRITICAL: "🔴",
            ErrorSeverity.HIGH: "🟠", 
            ErrorSeverity.MEDIUM: "🟡",
            ErrorSeverity.LOW: "🔵"
        }
        
        severity_icon = severity_icons.get(alert.severity, "⚪")
        
        print(f"\n{severity_icon} Alert {index}/{total}: {alert.severity.value.upper()} - {alert.category.value.replace('_', ' ').title()}")
        print(f"   📝 Issue: {alert.user_friendly_description}")
        print(f"   🔧 Component: {alert.component}")
        
        if alert.query_id:
            print(f"   🎯 Query ID: {alert.query_id}")
        
        # Show top 2 solutions
        if alert.suggested_solutions:
            print(f"   💡 Quick Fixes:")
            for i, solution in enumerate(alert.suggested_solutions[:2], 1):
                print(f"      {i}. {solution}")
        
        print(f"   🕒 Time: {alert.timestamp.strftime('%H:%M:%S')}")
        print(f"   🆔 Error ID: {alert.error_id}")
    
    def display_error_summary(self) -> str:
        """Display comprehensive error summary."""
        try:
            summary = get_error_summary()
            
            if summary['active_errors'] == 0:
                return "✅ No active errors detected. System is running smoothly!"
            
            output = self._format_error_summary(summary)
            return output
            
        except Exception as e:
            return f"⚠️ Unable to retrieve error summary: {e}"
    
    def _format_error_summary(self, summary: Dict[str, Any]) -> str:
        """Format error summary for display."""
        stats = summary['statistics']
        categories = summary['categories']
        recent_errors = summary['recent_errors']
        
        output = f"""
╔══════════════════════════════════════════════════════════════╗
║                        ERROR SUMMARY                         ║
╠══════════════════════════════════════════════════════════════╣
║  📊 Current Status:                                          ║
║     Active Errors: {summary['active_errors']:<10} Pending Notifications: {summary['pending_notifications']:<10}  ║
║                                                              ║
║  📈 Statistics:                                              ║
║     Total Errors: {stats['total_errors']:<11} Critical: {stats['critical_errors']:<11}          ║
║     High Priority: {stats['high_priority_errors']:<10} Resolved: {stats['resolved_errors']:<11}          ║
║     User Notifications: {stats['user_notifications']:<10}                        ║
║                                                              ║"""
        
        if categories:
            output += "║  🏷️ Error Categories:                                        ║\n"
            for category, count in categories.items():
                category_display = category.replace('_', ' ').title()
                output += f"║     {category_display}: {count:<10}                                  ║\n"
            output += "║                                                              ║\n"
        
        if recent_errors:
            output += "║  🕒 Recent Errors:                                           ║\n"
            for error in recent_errors:
                severity_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🔵"}.get(error['severity'], "⚪")
                output += f"║     {severity_icon} {error['timestamp']} - {error['description'][:35]:<35} ║\n"
            output += "║                                                              ║\n"
        
        output += "╚══════════════════════════════════════════════════════════════╝"
        
        return output
    
    def get_notification_stats(self) -> Dict[str, Any]:
        """Get notification handler statistics."""
        return {
            'notifications_sent': self.notification_count,
            'system_active': self.error_alert_system is not None
        }

def display_startup_errors():
    """Check and display any startup errors."""
    handler = ErrorNotificationHandler()
    return handler.check_and_display_errors()

def display_error_summary():
    """Display error summary command."""
    handler = ErrorNotificationHandler()
    return handler.display_error_summary()

def check_for_errors_during_conversation():
    """Check for errors during conversation (non-blocking)."""
    try:
        handler = ErrorNotificationHandler()
        return handler.check_and_display_errors()
    except Exception:
        return False

# Test function
def test_error_notifications():
    """Test the error notification system."""
    print("🧪 Testing Error Notification System")
    print("=" * 50)
    
    # Create some test errors
    from universal_logging.error_alert_system import ErrorAlertSystem
    
    system = ErrorAlertSystem()
    
    # Add test errors
    test_errors = [
        (ValueError("Model file not found"), "model loading", "CHATBOT"),
        (ConnectionError("Memory system unavailable"), "memory init", "MEMORY"),
        (AttributeError("Method not found"), "integration", "TTS")
    ]
    
    print("\n🚨 Adding test errors...")
    for error, context, component in test_errors:
        system.add_error(component, error, context)
    
    # Test notifications
    print("\n🔔 Testing error notifications...")
    handler = ErrorNotificationHandler()
    handler.check_and_display_errors()
    
    print("\n📊 Testing error summary...")
    summary = handler.display_error_summary()
    print(summary)
    
    system.shutdown()
    print("\n✅ Error notification test completed!")

if __name__ == "__main__":
    test_error_notifications()
