# memory_config.py

"""
Configuration settings for the Adrina Memory System
"""

import os
from typing import Dict, Any

# Embedding Service Configuration
# Local embeddings (preferred - 100% offline)
LOCAL_MODEL_PATH = os.getenv("LOCAL_MODEL_PATH", "models/nomic-embed-text")
PREFER_LOCAL_EMBEDDINGS = os.getenv("PREFER_LOCAL_EMBEDDINGS", "true").lower() == "true"

# Ollama Configuration (fallback)
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "nomic-embed-text:latest")
OLLAMA_TIMEOUT = int(os.getenv("OLLAMA_TIMEOUT", "30"))
OLLAMA_MAX_RETRIES = int(os.getenv("OLLAMA_MAX_RETRIES", "3"))

# ChromaDB Configuration
MEMORY_DB_PATH = os.getenv("MEMORY_DB_PATH", "./memory/memory_db")
COLLECTION_NAME = os.getenv("COLLECTION_NAME", "adrina_memories")

# Memory Storage Settings
MIN_TEXT_LENGTH = int(os.getenv("MIN_TEXT_LENGTH", "10"))
MAX_TEXT_LENGTH = int(os.getenv("MAX_TEXT_LENGTH", "2000"))
SIMILARITY_THRESHOLD = float(os.getenv("SIMILARITY_THRESHOLD", "0.0"))

# Memory Types Configuration
MEMORY_TYPES = {
    "conversation": {
        "description": "User-AI conversation exchanges",
        "retention_days": 30,
        "max_items": 1000
    },
    "fact": {
        "description": "Factual information and knowledge",
        "retention_days": 365,
        "max_items": 500
    },
    "preference": {
        "description": "User preferences and settings",
        "retention_days": 180,
        "max_items": 100
    },
    "context": {
        "description": "Contextual information for conversations",
        "retention_days": 7,
        "max_items": 200
    }
}

# Retrieval Settings
DEFAULT_MAX_RESULTS = int(os.getenv("DEFAULT_MAX_RESULTS", "5"))
DEFAULT_TIME_WINDOW_HOURS = int(os.getenv("DEFAULT_TIME_WINDOW_HOURS", "24"))
CONTEXT_MAX_ITEMS = int(os.getenv("CONTEXT_MAX_ITEMS", "3"))

# Performance Settings
EMBEDDING_BATCH_SIZE = int(os.getenv("EMBEDDING_BATCH_SIZE", "10"))
EMBEDDING_DELAY_MS = int(os.getenv("EMBEDDING_DELAY_MS", "100"))

# Logging Configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Quality Filters
QUALITY_FILTERS = {
    "min_unique_word_ratio": 0.5,  # Minimum ratio of unique words
    "skip_filler_responses": True,  # Skip common filler responses
    "min_word_count": 3,           # Minimum number of words
}

# Filler response patterns (regex patterns to skip)
FILLER_PATTERNS = [
    r'^(ok|okay|yes|no|sure|thanks|thank you)\.?$',
    r'^(i see|i understand|got it)\.?$',
    r'^(hello|hi|hey)\.?$',
    r'^(bye|goodbye|see you)\.?$'
]

def get_memory_config() -> Dict[str, Any]:
    """Get complete memory system configuration."""
    return {
        "embedding": {
            "prefer_local": PREFER_LOCAL_EMBEDDINGS,
            "local_model_path": LOCAL_MODEL_PATH
        },
        "ollama": {
            "base_url": OLLAMA_BASE_URL,
            "model": EMBEDDING_MODEL,
            "timeout": OLLAMA_TIMEOUT,
            "max_retries": OLLAMA_MAX_RETRIES
        },
        "chromadb": {
            "persist_directory": MEMORY_DB_PATH,
            "collection_name": COLLECTION_NAME
        },
        "storage": {
            "min_text_length": MIN_TEXT_LENGTH,
            "max_text_length": MAX_TEXT_LENGTH,
            "similarity_threshold": SIMILARITY_THRESHOLD
        },
        "memory_types": MEMORY_TYPES,
        "retrieval": {
            "default_max_results": DEFAULT_MAX_RESULTS,
            "default_time_window_hours": DEFAULT_TIME_WINDOW_HOURS,
            "context_max_items": CONTEXT_MAX_ITEMS
        },
        "performance": {
            "embedding_batch_size": EMBEDDING_BATCH_SIZE,
            "embedding_delay_ms": EMBEDDING_DELAY_MS
        },
        "quality": QUALITY_FILTERS,
        "filler_patterns": FILLER_PATTERNS,
        "logging": {
            "level": LOG_LEVEL,
            "format": LOG_FORMAT
        }
    }

def validate_config() -> bool:
    """Validate configuration settings."""
    try:
        # Check required directories
        os.makedirs(MEMORY_DB_PATH, exist_ok=True)
        
        # Validate numeric ranges
        assert 1 <= MIN_TEXT_LENGTH <= MAX_TEXT_LENGTH
        assert 0.0 <= SIMILARITY_THRESHOLD <= 1.0
        assert DEFAULT_MAX_RESULTS > 0
        assert EMBEDDING_BATCH_SIZE > 0
        
        return True
    except Exception as e:
        print(f"Configuration validation failed: {e}")
        return False

# Initialize configuration on import
if __name__ == "__main__":
    config = get_memory_config()
    print("Memory System Configuration:")
    print("=" * 40)
    
    for section, settings in config.items():
        print(f"\n{section.upper()}:")
        if isinstance(settings, dict):
            for key, value in settings.items():
                print(f"  {key}: {value}")
        else:
            print(f"  {settings}")
    
    print(f"\nConfiguration valid: {validate_config()}")
