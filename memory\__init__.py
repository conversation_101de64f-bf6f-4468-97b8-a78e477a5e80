# memory/__init__.py

"""
Adrina Memory System

A sophisticated memory system for the Adrina AI Assistant that uses ChromaDB 
for vector storage and Ollama's nomic-embed-text model for generating embeddings.

This system allows Adrina to remember conversations, facts, and user preferences 
for more contextual and personalized interactions.
"""

from .memory_manager import MemoryManager
from .ollama_embedding_service import OllamaEmbeddingService
from .local_embedding_service import LocalEmbeddingService, OfflineEmbeddingService
from .offline_memory_manager import OfflineMemoryManager
from .chroma_storage import ChromaDBStorage
from config.memory_config import get_memory_config, validate_config

__version__ = "2.0.0"
__author__ = "Adrina AI Assistant"

# Main exports
__all__ = [
    "MemoryManager",
    "OllamaEmbeddingService",
    "LocalEmbeddingService",
    "OfflineEmbeddingService",
    "OfflineMemoryManager",
    "ChromaDBStorage",
    "get_memory_config",
    "validate_config"
]

# Default memory manager instance (lazy initialization)
_default_memory_manager = None

def get_default_memory_manager():
    """Get the default memory manager instance (singleton pattern)."""
    global _default_memory_manager
    if _default_memory_manager is None:
        _default_memory_manager = MemoryManager()
    return _default_memory_manager

def reset_default_memory_manager():
    """Reset the default memory manager (useful for testing)."""
    global _default_memory_manager
    _default_memory_manager = None
