# memory_manager.py

from typing import List, Dict, Any, Optional, <PERSON>ple
import logging
from datetime import datetime, timedelta
import re
import hashlib

from .ollama_embedding_service import OllamaEmbeddingService
from .chroma_storage import ChromaDBStorage
from config.memory_config import SIMILARITY_THRESHOLD

class MemoryManager:
    """
    Central memory management system for Adrina AI Assistant.
    Handles storing, retrieving, and managing different types of memories.
    """
    
    def __init__(self,
                 persist_directory: str = "./memory/memory_db",
                 collection_name: str = "adrina_memories",
                 ollama_url: str = "http://localhost:11434",
                 embedding_model: str = "nomic-embed-text:latest"):
        """
        Initialize the memory manager.
        
        Args:
            persist_directory: Directory for ChromaDB persistence
            collection_name: ChromaDB collection name
            ollama_url: Ollama server URL
            embedding_model: Embedding model name
        """
        self.logger = logging.getLogger(__name__)
        
        # Initialize services
        self.embedding_service = OllamaEmbeddingService(
            base_url=ollama_url,
            model=embedding_model
        )
        
        self.storage = ChromaDBStorage(
            persist_directory=persist_directory,
            collection_name=collection_name
        )
        
        # Memory configuration
        self.min_text_length = 10  # Minimum text length to store
        self.max_text_length = 2000  # Maximum text length to store
        self.similarity_threshold = SIMILARITY_THRESHOLD  # Minimum similarity for relevant memories
        
        self.logger.info("MemoryManager initialized successfully")
    
    def store_conversation(self, 
                          user_input: str, 
                          ai_response: str, 
                          metadata: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        Store a conversation exchange in memory.
        
        Args:
            user_input: User's input text
            ai_response: AI's response text
            metadata: Additional metadata
            
        Returns:
            List of memory IDs that were created
        """
        memory_ids = []
        timestamp = datetime.now().isoformat()
        
        # Prepare base metadata
        base_metadata = {
            "conversation_id": self._generate_conversation_id(user_input, ai_response),
            "timestamp": timestamp
        }
        
        if metadata:
            base_metadata.update(metadata)
        
        # Store user input
        if self._should_store_text(user_input):
            user_embedding = self.embedding_service.get_embedding(user_input)
            if user_embedding:
                user_metadata = base_metadata.copy()
                user_metadata.update({
                    "speaker": "user",
                    "message_type": "input"
                })
                
                memory_id = self.storage.add_memory(
                    text=user_input,
                    embedding=user_embedding,
                    memory_type="conversation",
                    metadata=user_metadata
                )
                memory_ids.append(memory_id)
                self.logger.debug(f"Stored user input: {memory_id}")
        
        # Store AI response
        if self._should_store_text(ai_response):
            ai_embedding = self.embedding_service.get_embedding(ai_response)
            if ai_embedding:
                ai_metadata = base_metadata.copy()
                ai_metadata.update({
                    "speaker": "ai",
                    "message_type": "response"
                })
                
                memory_id = self.storage.add_memory(
                    text=ai_response,
                    embedding=ai_embedding,
                    memory_type="conversation",
                    metadata=ai_metadata
                )
                memory_ids.append(memory_id)
                self.logger.debug(f"Stored AI response: {memory_id}")
        
        return memory_ids
    
    def store_fact(self, 
                   fact_text: str, 
                   category: str = "general",
                   importance: int = 5,
                   metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Store a factual piece of information.
        
        Args:
            fact_text: The factual information
            category: Category of the fact
            importance: Importance level (1-10)
            metadata: Additional metadata
            
        Returns:
            Memory ID if successful, None otherwise
        """
        if not self._should_store_text(fact_text):
            return None
        
        embedding = self.embedding_service.get_embedding(fact_text)
        if not embedding:
            return None
        
        fact_metadata = {
            "category": category,
            "importance": importance,
            "timestamp": datetime.now().isoformat()
        }
        
        if metadata:
            fact_metadata.update(metadata)
        
        memory_id = self.storage.add_memory(
            text=fact_text,
            embedding=embedding,
            memory_type="fact",
            metadata=fact_metadata
        )
        
        self.logger.debug(f"Stored fact: {memory_id}")
        return memory_id
    
    def store_preference(self, 
                        preference_text: str, 
                        user_id: str = "default",
                        metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Store a user preference.
        
        Args:
            preference_text: The preference information
            user_id: User identifier
            metadata: Additional metadata
            
        Returns:
            Memory ID if successful, None otherwise
        """
        if not self._should_store_text(preference_text):
            return None
        
        embedding = self.embedding_service.get_embedding(preference_text)
        if not embedding:
            return None
        
        pref_metadata = {
            "user_id": user_id,
            "timestamp": datetime.now().isoformat()
        }
        
        if metadata:
            pref_metadata.update(metadata)
        
        memory_id = self.storage.add_memory(
            text=preference_text,
            embedding=embedding,
            memory_type="preference",
            metadata=pref_metadata
        )
        
        self.logger.debug(f"Stored preference: {memory_id}")
        return memory_id
    
    def retrieve_relevant_memories(self, 
                                  query_text: str, 
                                  memory_types: Optional[List[str]] = None,
                                  max_results: int = 5,
                                  time_window_hours: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Retrieve memories relevant to a query.
        
        Args:
            query_text: Text to search for similar memories
            memory_types: Types of memories to search (optional)
            max_results: Maximum number of results
            time_window_hours: Only search within this time window (optional)
            
        Returns:
            List of relevant memories with similarity scores
        """
        # Get query embedding
        query_embedding = self.embedding_service.get_embedding(query_text)
        if not query_embedding:
            self.logger.warning("Failed to get embedding for query")
            return []
        
        # Prepare time filter if specified
        time_filter = None
        if time_window_hours:
            cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
            time_filter = {"after": cutoff_time.isoformat()}
        
        # Search for similar memories
        memories = self.storage.search_memories(
            query_embedding=query_embedding,
            n_results=max_results,
            memory_types=memory_types,
            time_filter=time_filter
        )
        
        # Filter by similarity threshold
        relevant_memories = [
            memory for memory in memories 
            if memory["similarity_score"] >= self.similarity_threshold
        ]
        
        self.logger.debug(f"Found {len(relevant_memories)} relevant memories for query: {query_text[:50]}...")
        return relevant_memories
    
    def get_conversation_context(self, 
                               current_input: str, 
                               max_context_items: int = 3) -> str:
        """
        Get relevant conversation context for the current input.
        
        Args:
            current_input: Current user input
            max_context_items: Maximum number of context items to include
            
        Returns:
            Formatted context string
        """
        # Get recent conversation memories
        recent_memories = self.retrieve_relevant_memories(
            query_text=current_input,
            memory_types=["conversation"],
            max_results=max_context_items,
            time_window_hours=24  # Look back 24 hours
        )
        
        if not recent_memories:
            return ""
        
        # Format context
        context_parts = []
        for memory in recent_memories:
            speaker = memory["metadata"].get("speaker", "unknown")
            text = memory["text"]
            timestamp = memory["metadata"].get("timestamp", "")
            
            # Format timestamp for readability
            if timestamp:
                try:
                    dt = datetime.fromisoformat(timestamp)
                    time_str = dt.strftime("%H:%M")
                    context_parts.append(f"[{time_str}] {speaker.title()}: {text}")
                except:
                    context_parts.append(f"{speaker.title()}: {text}")
            else:
                context_parts.append(f"{speaker.title()}: {text}")
        
        return "\n".join(context_parts)
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """Get a summary of stored memories."""
        stats = self.storage.get_memory_stats()
        
        # Add embedding service status
        stats["embedding_service_available"] = self.embedding_service.is_available()
        
        return stats
    
    def _should_store_text(self, text: str) -> bool:
        """Check if text should be stored based on length and content."""
        if not text or not text.strip():
            return False
        
        text_length = len(text.strip())
        if text_length < self.min_text_length or text_length > self.max_text_length:
            return False
        
        # Skip very repetitive or low-quality text
        if self._is_low_quality_text(text):
            return False
        
        return True
    
    def _is_low_quality_text(self, text: str) -> bool:
        """Check if text is low quality and shouldn't be stored."""
        text = text.strip().lower()
        
        # Skip very short responses
        if len(text.split()) < 3:
            return True
        
        # Skip repetitive text
        words = text.split()
        if len(set(words)) / len(words) < 0.5:  # Less than 50% unique words
            return True
        
        # Skip common filler responses
        filler_patterns = [
            r'^(ok|okay|yes|no|sure|thanks|thank you)\.?$',
            r'^(i see|i understand|got it)\.?$',
            r'^(hello|hi|hey)\.?$'
        ]
        
        for pattern in filler_patterns:
            if re.match(pattern, text):
                return True
        
        return False
    
    def _generate_conversation_id(self, user_input: str, ai_response: str) -> str:
        """Generate a unique conversation ID."""
        combined_text = f"{user_input}|{ai_response}|{datetime.now().isoformat()}"
        return hashlib.md5(combined_text.encode()).hexdigest()[:16]


# Testing code
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # Test the memory manager
    memory_manager = MemoryManager()
    
    # Test storing a conversation
    user_input = "What's the weather like today?"
    ai_response = "I don't have access to real-time weather data, but I can help you find weather information through various weather services."
    
    memory_ids = memory_manager.store_conversation(user_input, ai_response)
    print(f"Stored conversation with IDs: {memory_ids}")
    
    # Test retrieving relevant memories
    query = "weather information"
    relevant = memory_manager.retrieve_relevant_memories(query)
    print(f"Found {len(relevant)} relevant memories for '{query}'")
    
    # Test memory summary
    summary = memory_manager.get_memory_summary()
    print(f"Memory summary: {summary}")
