#!/usr/bin/env python3
"""
Command handlers for Adrina AI Assistant v2.0

Contains command processing functions for the interactive interface.
"""

def run_classification_test(connector):
    """Run classification test and display results."""
    if not connector.processor or not connector.processor.multi_task_assistant:
        print("❌ Multi-Task Assistant not available for testing.")
        return
    
    print("🧪 Running classification tests...")
    test_results = connector.processor.multi_task_assistant.test_classification()
    
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    CLASSIFICATION TEST RESULTS              ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  🎯 Overall Results:                                         ║
    ║     Accuracy: {test_results['accuracy']:.1f}%                                        ║
    ║     Correct:  {test_results['correct']}/{test_results['total']}                                            ║
    ║     Avg Time: {test_results['avg_time']:.3f}s                                    ║
    ║                                                              ║
    ║  📝 Individual Test Results:                                 ║""")
    
    for result in test_results['results']:
        status = "✅" if result['correct'] else "❌"
        query_short = result['query'][:35] + "..." if len(result['query']) > 35 else result['query']
        print(f"    ║     {status} {query_short:<40} {result['predicted']:<6} {result['time']:.3f}s ║")
    
    print("    ╚══════════════════════════════════════════════════════════════╝")
