#!/usr/bin/env python3
"""
Semantic Query Classifier for Adrina AI Assistant v2.0

Uses sentence embeddings for intelligent query classification
without hardcoded patterns. More flexible than pattern matching,
faster than full LLM inference.
"""

import os
import sys
import time
import numpy as np
from typing import Tuple, List
from dataclasses import dataclass
from enum import Enum

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: sentence-transformers not installed. Install with: pip install sentence-transformers")
    SENTENCE_TRANSFORMERS_AVAILABLE = False

from universal_logging.universal_logger import ComponentType, get_logger

class QueryCategory(Enum):
    """Query classification categories."""
    SIMPLE = "SIMPLE"
    COMPLEX = "COMPLEX"
    RESEARCH = "RESEARCH"
    UNKNOWN = "UNKNOWN"

@dataclass
class ClassificationResult:
    """Result of semantic classification."""
    category: QueryCategory
    confidence: float
    processing_time: float
    reasoning: str = ""

class SemanticClassifier:
    """
    Semantic query classifier using sentence embeddings.
    
    More flexible than hardcoded patterns, faster than full LLM.
    Uses pre-trained sentence transformer models for semantic understanding.
    """
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.logger = get_logger()
        self.model = None
        self.model_name = model_name
        self.is_loaded = False
        
        # Example queries for each category (training data)
        self.category_examples = {
            QueryCategory.SIMPLE: [
                "hi", "hello", "hey there", "good morning", "good evening",
                "how are you", "what's your name", "who are you",
                "tell me a joke", "make me laugh", "say something funny",
                "goodbye", "bye", "see you later", "thanks", "thank you",
                "yes", "no", "okay", "sure", "maybe"
            ],
            QueryCategory.COMPLEX: [
                "create a website", "build an application", "develop software",
                "design a system", "implement authentication", "make a database",
                "urgent: fix this bug", "priority: deploy the code", "help me code",
                "write a program", "configure the server", "set up the environment",
                "troubleshoot the issue", "debug this error", "optimize performance",
                "integrate with API", "deploy to production", "manage the project"
            ],
            QueryCategory.RESEARCH: [
                "explain machine learning", "what is quantum computing", "how does AI work",
                "describe the algorithm", "analyze this concept", "research this topic",
                "tell me about blockchain", "what are neural networks", "how do databases work",
                "explain in detail", "provide comprehensive information", "give me an overview",
                "what's the theory behind", "how does this technology work", "describe the process",
                "what are the principles of", "explain the fundamentals", "analyze the approach"
            ]
        }
        
        # Pre-computed embeddings for examples
        self.category_embeddings = {}
        
        # Statistics
        self.stats = {
            'total_classifications': 0,
            'avg_processing_time': 0.0,
            'category_counts': {cat.value: 0 for cat in QueryCategory}
        }
        
        # Load model
        self._load_model()
    
    def _load_model(self):
        """Load the sentence transformer model."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            self.logger.log_warning(ComponentType.TASK_CLASSIFIER,
                                  "sentence-transformers not available - using fallback")
            return
        
        try:
            self.logger.log_system_event(f"🧠 Loading semantic model: {self.model_name}")
            start_time = time.time()
            
            # Load lightweight sentence transformer
            self.model = SentenceTransformer(self.model_name)
            
            # Pre-compute embeddings for category examples
            self._precompute_category_embeddings()
            
            load_time = time.time() - start_time
            self.is_loaded = True
            
            self.logger.log_system_event(f"✅ Semantic model loaded in {load_time:.2f}s")
            
        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "loading semantic model")
            self.is_loaded = False
    
    def _precompute_category_embeddings(self):
        """Pre-compute embeddings for category examples."""
        for category, examples in self.category_examples.items():
            embeddings = self.model.encode(examples)
            # Use mean embedding as category representation
            self.category_embeddings[category] = np.mean(embeddings, axis=0)
    
    def classify_query(self, query: str) -> ClassificationResult:
        """
        Classify query using semantic similarity.
        
        Args:
            query: User input to classify
            
        Returns:
            ClassificationResult with category and confidence
        """
        start_time = time.time()
        
        # Fallback to enhanced patterns if model not loaded
        if not self.is_loaded or not self.model:
            return self._enhanced_pattern_classification(query, start_time)
        
        try:
            # Encode the query
            query_embedding = self.model.encode([query])[0]
            
            # Calculate similarities to each category
            similarities = {}
            for category, category_embedding in self.category_embeddings.items():
                similarity = np.dot(query_embedding, category_embedding) / (
                    np.linalg.norm(query_embedding) * np.linalg.norm(category_embedding)
                )
                similarities[category] = similarity
            
            # Find best match
            best_category = max(similarities, key=similarities.get)
            confidence = similarities[best_category]
            
            # Adjust confidence based on similarity score
            if confidence > 0.7:
                adjusted_confidence = 0.9
            elif confidence > 0.5:
                adjusted_confidence = 0.7
            elif confidence > 0.3:
                adjusted_confidence = 0.5
            else:
                adjusted_confidence = 0.3
                best_category = QueryCategory.UNKNOWN
            
            processing_time = time.time() - start_time
            self._update_stats(best_category, processing_time)
            
            return ClassificationResult(
                category=best_category,
                confidence=adjusted_confidence,
                processing_time=processing_time,
                reasoning=f"Semantic similarity: {confidence:.3f}"
            )
            
        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "semantic classification")
            return self._enhanced_pattern_classification(query, start_time)
    
    def _enhanced_pattern_classification(self, query: str, start_time: float) -> ClassificationResult:
        """Enhanced pattern-based fallback with more flexible matching."""
        query_lower = query.lower().strip()
        words = query_lower.split()
        
        # Enhanced simple indicators
        simple_indicators = {
            'greetings': ['hi', 'hello', 'hey', 'morning', 'evening', 'afternoon'],
            'identity': ['name', 'who', 'you'],
            'casual': ['joke', 'funny', 'laugh', 'story'],
            'farewell': ['bye', 'goodbye', 'later', 'thanks', 'thank'],
            'short_responses': ['yes', 'no', 'okay', 'sure', 'maybe']
        }
        
        # Enhanced complex indicators
        complex_indicators = {
            'creation': ['create', 'build', 'make', 'develop', 'design', 'implement'],
            'technical': ['code', 'program', 'software', 'system', 'database', 'server'],
            'urgent': ['urgent', 'priority', 'critical', 'asap', 'quickly'],
            'help': ['help', 'assist', 'support', 'fix', 'solve', 'debug'],
            'action': ['configure', 'setup', 'install', 'deploy', 'manage']
        }
        
        # Enhanced research indicators
        research_indicators = {
            'explanation': ['explain', 'describe', 'tell', 'what', 'how', 'why'],
            'analysis': ['analyze', 'research', 'study', 'examine', 'investigate'],
            'detail': ['detail', 'comprehensive', 'thorough', 'complete', 'overview'],
            'theory': ['theory', 'principle', 'concept', 'fundamental', 'basis']
        }
        
        # Calculate scores for each category
        simple_score = self._calculate_category_score(words, simple_indicators)
        complex_score = self._calculate_category_score(words, complex_indicators)
        research_score = self._calculate_category_score(words, research_indicators)
        
        # Length-based adjustments
        word_count = len(words)
        if word_count <= 3:
            simple_score += 0.3
        elif word_count > 15:
            research_score += 0.2
        elif word_count > 8:
            complex_score += 0.1
        
        # Determine category and confidence
        scores = {
            QueryCategory.SIMPLE: simple_score,
            QueryCategory.COMPLEX: complex_score,
            QueryCategory.RESEARCH: research_score
        }
        
        best_category = max(scores, key=scores.get)
        best_score = scores[best_category]
        
        # Convert score to confidence
        if best_score > 0.5:
            confidence = 0.8
        elif best_score > 0.3:
            confidence = 0.6
        elif best_score > 0.1:
            confidence = 0.4
        else:
            confidence = 0.3
            best_category = QueryCategory.SIMPLE  # Default fallback
        
        processing_time = time.time() - start_time
        self._update_stats(best_category, processing_time)
        
        return ClassificationResult(
            category=best_category,
            confidence=confidence,
            processing_time=processing_time,
            reasoning=f"Enhanced pattern match: {best_score:.2f}"
        )
    
    def _calculate_category_score(self, words: List[str], indicators: dict) -> float:
        """Calculate score for a category based on indicator matches."""
        total_score = 0.0
        total_indicators = sum(len(indicator_list) for indicator_list in indicators.values())
        
        for indicator_type, indicator_list in indicators.items():
            matches = sum(1 for word in words if any(indicator in word for indicator in indicator_list))
            if matches > 0:
                # Weight by indicator type importance and normalize
                type_weight = 1.0  # Could be adjusted per type
                total_score += (matches / len(words)) * type_weight
        
        return min(total_score, 1.0)  # Cap at 1.0
    
    def _update_stats(self, category: QueryCategory, processing_time: float):
        """Update classification statistics."""
        self.stats['total_classifications'] += 1
        self.stats['category_counts'][category.value] += 1
        
        # Update average processing time
        total = self.stats['total_classifications']
        current_avg = self.stats['avg_processing_time']
        self.stats['avg_processing_time'] = ((current_avg * (total - 1)) + processing_time) / total
    
    def get_stats(self) -> dict:
        """Get classification statistics."""
        return {
            'model_loaded': self.is_loaded,
            'model_name': self.model_name,
            'total_classifications': self.stats['total_classifications'],
            'avg_processing_time': self.stats['avg_processing_time'],
            'category_distribution': self.stats['category_counts'],
            'semantic_available': SENTENCE_TRANSFORMERS_AVAILABLE
        }

# Test function
def test_semantic_classifier():
    """Test the semantic classifier."""
    print("🧪 Testing Semantic Classifier")
    print("=" * 50)
    
    classifier = SemanticClassifier()
    
    test_queries = [
        "good morning",
        "can you help me understand machine learning?",
        "design a secure authentication system",
        "what's the theory behind neural networks?",
        "build me a website",
        "describe quantum computing",
        "thanks for your help",
        "develop a mobile app"
    ]
    
    for query in test_queries:
        result = classifier.classify_query(query)
        print(f"Query: '{query}'")
        print(f"  Category: {result.category.value}")
        print(f"  Confidence: {result.confidence:.2f}")
        print(f"  Time: {result.processing_time:.3f}s")
        print(f"  Reasoning: {result.reasoning}")
        print()
    
    print("📊 Final Statistics:")
    stats = classifier.get_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_semantic_classifier()
