# ollama_embedding_service.py

import requests
import json
import logging
from typing import List, Optional, Dict, Any
import time

class OllamaEmbeddingService:
    """Service to interact with Ollama's nomic-embed-text model for generating embeddings."""
    
    def __init__(self,
                 base_url: str = "http://localhost:11434",
                 model: str = "nomic-embed-text:latest",
                 timeout: int = 30,
                 max_retries: int = 3):
        """
        Initialize the Ollama embedding service.
        
        Args:
            base_url: Ollama server URL
            model: Embedding model name (default: nomic-embed-text:latest)
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
        """
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.logger = logging.getLogger(__name__)
        
        # Test connection on initialization
        self._test_connection()
    
    def _test_connection(self) -> bool:
        """Test connection to Ollama server."""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                
                if self.model not in model_names:
                    self.logger.warning(f"Model '{self.model}' not found in available models: {model_names}")
                    self.logger.info(f"You may need to pull the model: ollama pull {self.model}")
                else:
                    self.logger.info(f"Successfully connected to Ollama. Model '{self.model}' is available.")
                return True
            else:
                self.logger.error(f"Failed to connect to Ollama server: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to connect to Ollama server: {e}")
            return False
    
    def get_embedding(self, text: str) -> Optional[List[float]]:
        """
        Get embedding for a single text.
        
        Args:
            text: Text to embed
            
        Returns:
            List of floats representing the embedding, or None if failed
        """
        if not text or not text.strip():
            self.logger.warning("Empty text provided for embedding")
            return None
        
        for attempt in range(self.max_retries):
            try:
                payload = {
                    "model": self.model,
                    "prompt": text.strip()
                }
                
                response = requests.post(
                    f"{self.base_url}/api/embeddings",
                    json=payload,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    embedding = result.get('embedding')
                    
                    if embedding and isinstance(embedding, list):
                        self.logger.debug(f"Generated embedding of dimension {len(embedding)} for text: {text[:50]}...")
                        return embedding
                    else:
                        self.logger.error(f"Invalid embedding response: {result}")
                        return None
                else:
                    self.logger.error(f"Embedding request failed: {response.status_code} - {response.text}")
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"Embedding request timeout (attempt {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Embedding request failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)
        
        self.logger.error(f"Failed to get embedding after {self.max_retries} attempts")
        return None
    
    def get_embeddings_batch(self, texts: List[str], batch_size: int = 10) -> List[Optional[List[float]]]:
        """
        Get embeddings for multiple texts in batches.
        
        Args:
            texts: List of texts to embed
            batch_size: Number of texts to process at once
            
        Returns:
            List of embeddings (same order as input texts)
        """
        if not texts:
            return []
        
        embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            self.logger.info(f"Processing embedding batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
            
            batch_embeddings = []
            for text in batch:
                embedding = self.get_embedding(text)
                batch_embeddings.append(embedding)
                
                # Small delay to avoid overwhelming the server
                time.sleep(0.1)
            
            embeddings.extend(batch_embeddings)
        
        return embeddings
    
    def get_model_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the embedding model."""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=self.timeout)
            if response.status_code == 200:
                models = response.json().get('models', [])
                for model in models:
                    if model['name'] == self.model:
                        return model
            return None
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to get model info: {e}")
            return None
    
    def is_available(self) -> bool:
        """Check if the embedding service is available."""
        return self._test_connection()


# Configuration for testing
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Test the embedding service
    service = OllamaEmbeddingService()
    
    if service.is_available():
        # Test single embedding
        test_text = "Hello, this is a test sentence for embedding."
        embedding = service.get_embedding(test_text)
        
        if embedding:
            print(f"Successfully generated embedding with {len(embedding)} dimensions")
            print(f"First 5 values: {embedding[:5]}")
        else:
            print("Failed to generate embedding")
        
        # Test batch embeddings
        test_texts = [
            "This is the first test sentence.",
            "Here is another sentence to embed.",
            "And this is the third one."
        ]
        
        embeddings = service.get_embeddings_batch(test_texts)
        print(f"Generated {len([e for e in embeddings if e is not None])} out of {len(test_texts)} embeddings")
    else:
        print("Ollama embedding service is not available")
