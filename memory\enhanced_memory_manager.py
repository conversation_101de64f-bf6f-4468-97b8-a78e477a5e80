#!/usr/bin/env python3
"""
Enhanced Memory Manager for Adrina AI Assistant v2.0

Intelligent memory manager that automatically chooses between:
- Local nomic embeddings (preferred, 100% offline)
- Ollama embeddings (fallback, requires Ollama server)

Provides seamless integration with existing code.
"""

import os
import sys
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from memory.local_embedding_service import LocalEmbeddingService
from memory.ollama_embedding_service import OllamaEmbeddingService
from memory.chroma_storage import ChromaDBStorage
from config.memory_config import (
    MIN_TEXT_LENGTH, MAX_TEXT_LENGTH, SIMILARITY_THRESHOLD,
    DEFAULT_MAX_RESULTS, CONTEXT_MAX_ITEMS
)

class EnhancedMemoryManager:
    """
    Enhanced memory manager with intelligent embedding service selection.
    
    Automatically chooses the best available embedding service:
    1. Local nomic embeddings (preferred - 100% offline)
    2. Ollama embeddings (fallback - requires server)
    
    Provides the same interface as the original MemoryManager.
    """
    
    def __init__(self,
                 persist_directory: str = "./memory/memory_db",
                 collection_name: str = "adrina_memories",
                 model_path: str = "models/nomic-embed-text",
                 ollama_url: str = "http://localhost:11434",
                 embedding_model: str = "nomic-embed-text:latest",
                 prefer_local: bool = True):
        """
        Initialize enhanced memory manager.
        
        Args:
            persist_directory: Directory for ChromaDB persistence
            collection_name: ChromaDB collection name
            model_path: Path to local nomic model
            ollama_url: Ollama server URL (fallback)
            embedding_model: Ollama embedding model name (fallback)
            prefer_local: Whether to prefer local embeddings over Ollama
        """
        self.logger = logging.getLogger(__name__)
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        
        # Configuration
        self.min_text_length = MIN_TEXT_LENGTH
        self.max_text_length = MAX_TEXT_LENGTH
        self.similarity_threshold = SIMILARITY_THRESHOLD
        
        # Initialize embedding service
        self.embedding_service = None
        self.service_type = None
        
        if prefer_local:
            self._try_local_then_ollama(model_path, ollama_url, embedding_model)
        else:
            self._try_ollama_then_local(model_path, ollama_url, embedding_model)
        
        # Initialize storage
        if self.embedding_service:
            self.storage = ChromaDBStorage(
                persist_directory=persist_directory,
                collection_name=collection_name
            )
            self.logger.info(f"✅ Enhanced memory manager initialized with {self.service_type} embeddings")
        else:
            self.storage = None
            self.logger.error("❌ No embedding service available - memory system disabled")
    
    def _try_local_then_ollama(self, model_path: str, ollama_url: str, embedding_model: str):
        """Try local embeddings first, then Ollama as fallback."""
        # Try local embeddings first
        try:
            local_service = LocalEmbeddingService(model_path=model_path)
            if local_service.is_available():
                self.embedding_service = local_service
                self.service_type = "Local"
                self.logger.info("🏠 Using local nomic embeddings (100% offline)")
                return
        except Exception as e:
            self.logger.debug(f"Local embeddings not available: {e}")
        
        # Fallback to Ollama
        try:
            ollama_service = OllamaEmbeddingService(
                base_url=ollama_url,
                model=embedding_model
            )
            if ollama_service.is_available():
                self.embedding_service = ollama_service
                self.service_type = "Ollama"
                self.logger.info("🌐 Using Ollama embeddings (requires server)")
                return
        except Exception as e:
            self.logger.debug(f"Ollama embeddings not available: {e}")
        
        self.logger.warning("⚠️ No embedding service available")
    
    def _try_ollama_then_local(self, model_path: str, ollama_url: str, embedding_model: str):
        """Try Ollama first, then local as fallback."""
        # Try Ollama first
        try:
            ollama_service = OllamaEmbeddingService(
                base_url=ollama_url,
                model=embedding_model
            )
            if ollama_service.is_available():
                self.embedding_service = ollama_service
                self.service_type = "Ollama"
                self.logger.info("🌐 Using Ollama embeddings (requires server)")
                return
        except Exception as e:
            self.logger.debug(f"Ollama embeddings not available: {e}")
        
        # Fallback to local
        try:
            local_service = LocalEmbeddingService(model_path=model_path)
            if local_service.is_available():
                self.embedding_service = local_service
                self.service_type = "Local"
                self.logger.info("🏠 Using local nomic embeddings (100% offline)")
                return
        except Exception as e:
            self.logger.debug(f"Local embeddings not available: {e}")
        
        self.logger.warning("⚠️ No embedding service available")
    
    def is_available(self) -> bool:
        """Check if the memory system is available."""
        return self.embedding_service is not None and self.storage is not None
    
    def get_embedding(self, text: str) -> Optional[List[float]]:
        """Get embedding for text using the active service."""
        if not self.embedding_service:
            return None
        
        # Use appropriate method based on service type
        if self.service_type == "Local":
            return self.embedding_service.generate_embedding(text)
        else:  # Ollama
            return self.embedding_service.get_embedding(text)
    
    def get_embeddings_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """Get embeddings for multiple texts."""
        if not self.embedding_service:
            return [None] * len(texts)
        
        # Use appropriate method based on service type
        if self.service_type == "Local":
            return self.embedding_service.generate_embeddings_batch(texts)
        else:  # Ollama
            return self.embedding_service.get_embeddings_batch(texts)
    
    def store_conversation(self, 
                          user_input: str, 
                          ai_response: str, 
                          metadata: Optional[Dict[str, Any]] = None) -> List[str]:
        """Store a conversation exchange in memory."""
        if not self.is_available():
            return []
        
        memory_ids = []
        timestamp = datetime.now().isoformat()
        
        # Prepare base metadata
        base_metadata = {
            "conversation_id": self._generate_conversation_id(user_input, ai_response),
            "timestamp": timestamp,
            "embedding_service": self.service_type
        }
        
        if metadata:
            base_metadata.update(metadata)
        
        # Store user input
        if self._should_store_text(user_input):
            user_embedding = self.get_embedding(user_input)
            if user_embedding:
                user_metadata = base_metadata.copy()
                user_metadata.update({
                    "memory_type": "user_input",
                    "role": "user"
                })
                
                user_id = self.storage.add_memory(
                    text=user_input,
                    embedding=user_embedding,
                    memory_type="conversation",
                    metadata=user_metadata
                )
                
                if user_id:
                    memory_ids.append(user_id)
        
        # Store AI response
        if self._should_store_text(ai_response):
            ai_embedding = self.get_embedding(ai_response)
            if ai_embedding:
                ai_metadata = base_metadata.copy()
                ai_metadata.update({
                    "memory_type": "ai_response",
                    "role": "assistant"
                })
                
                ai_id = self.storage.add_memory(
                    text=ai_response,
                    embedding=ai_embedding,
                    memory_type="conversation",
                    metadata=ai_metadata
                )
                
                if ai_id:
                    memory_ids.append(ai_id)
        
        return memory_ids
    
    def store_fact(self, 
                   fact_text: str, 
                   category: str = "general",
                   importance: int = 5,
                   metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """Store a factual piece of information."""
        if not self.is_available() or not self._should_store_text(fact_text):
            return None
        
        embedding = self.get_embedding(fact_text)
        if not embedding:
            return None
        
        fact_metadata = {
            "category": category,
            "importance": importance,
            "timestamp": datetime.now().isoformat(),
            "embedding_service": self.service_type
        }
        
        if metadata:
            fact_metadata.update(metadata)
        
        return self.storage.add_memory(
            text=fact_text,
            embedding=embedding,
            memory_type="fact",
            metadata=fact_metadata
        )
    
    def store_preference(self, 
                        preference_text: str, 
                        user_id: str = "default",
                        metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """Store a user preference."""
        if not self.is_available() or not self._should_store_text(preference_text):
            return None
        
        embedding = self.get_embedding(preference_text)
        if not embedding:
            return None
        
        pref_metadata = {
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
            "embedding_service": self.service_type
        }
        
        if metadata:
            pref_metadata.update(metadata)
        
        return self.storage.add_memory(
            text=preference_text,
            embedding=embedding,
            memory_type="preference",
            metadata=pref_metadata
        )
    
    def retrieve_relevant_memories(self, 
                                  query: str,
                                  memory_types: Optional[List[str]] = None,
                                  max_results: int = DEFAULT_MAX_RESULTS,
                                  similarity_threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """Retrieve memories relevant to a query."""
        if not self.is_available():
            return []
        
        query_embedding = self.get_embedding(query)
        if not query_embedding:
            return []
        
        threshold = similarity_threshold or self.similarity_threshold
        
        results = self.storage.search_memories(
            query_embedding=query_embedding,
            n_results=max_results,
            memory_types=memory_types
        )
        
        # Filter by similarity threshold
        filtered_results = []
        for result in results:
            if result.get('similarity', 0) >= threshold:
                filtered_results.append(result)
        
        return filtered_results
    
    def get_conversation_context(self, 
                               query: str,
                               max_context_items: int = CONTEXT_MAX_ITEMS,
                               time_window_hours: Optional[int] = None) -> str:
        """Get conversation context for a query."""
        if not self.is_available():
            return ""
        
        # Get recent conversation memories
        memories = self.retrieve_relevant_memories(
            query=query,
            memory_types=["conversation"],
            max_results=max_context_items
        )
        
        if not memories:
            return ""
        
        # Format context
        context_parts = []
        for memory in memories:
            role = memory["metadata"].get("role", "unknown")
            text = memory["text"]
            
            if role == "user":
                context_parts.append(f"User: {text}")
            elif role == "assistant":
                context_parts.append(f"Assistant: {text}")
            else:
                context_parts.append(f"Context: {text}")
        
        return "\n".join(context_parts)
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """Get a summary of stored memories."""
        if not self.is_available():
            return {
                "available": False,
                "embedding_service": None,
                "error": "Memory system not available"
            }
        
        stats = self.storage.get_memory_stats()
        stats.update({
            "available": True,
            "embedding_service": self.service_type,
            "embedding_service_available": True
        })
        
        return stats
    
    def _should_store_text(self, text: str) -> bool:
        """Check if text should be stored based on length and content."""
        if not text or not text.strip():
            return False
        
        text_length = len(text.strip())
        if text_length < self.min_text_length or text_length > self.max_text_length:
            return False
        
        return True
    
    def _generate_conversation_id(self, user_input: str, ai_response: str) -> str:
        """Generate a unique conversation ID."""
        import hashlib
        combined = f"{user_input[:50]}{ai_response[:50]}{datetime.now().isoformat()}"
        return hashlib.md5(combined.encode()).hexdigest()[:12]
