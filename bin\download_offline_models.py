#!/usr/bin/env python3
"""
Offline Models Downloader for Adrina AI Assistant v2.0

Downloads all required models for 100% offline operation:
- nomic-embed-text-v1 for local embeddings
- Optional: Tiny LLM for classification
- Optional: Additional models for enhanced functionality
"""

import os
import sys
import requests
import json
from pathlib import Path
from typing import Dict, List, Optional
import hashlib
import time

class ModelDownloader:
    """Downloads and manages AI models for offline operation."""
    
    def __init__(self, models_dir: str = "models"):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(exist_ok=True)
        
        # Model configurations
        self.models = {
            "nomic-embed-text-v1": {
                "description": "Local embedding model (REQUIRED for memory)",
                "size": "~500MB",
                "priority": "REQUIRED",
                "base_url": "https://huggingface.co/nomic-ai/nomic-embed-text-v1/resolve/main",
                "files": [
                    "config.json",
                    "pytorch_model.bin", 
                    "tokenizer.json",
                    "tokenizer_config.json",
                    "README.md"
                ],
                "local_dir": "nomic-embed-text"
            },
            "tinyllama-1.1b": {
                "description": "Tiny LLM for intelligent classification (OPTIONAL)",
                "size": "~637MB", 
                "priority": "OPTIONAL",
                "base_url": "https://huggingface.co/TheBloke/TinyLlama-1.1B-Chat-v1.0-GGUF/resolve/main",
                "files": [
                    "tinyllama-1.1b-chat-v1.0.q4_k_m.gguf"
                ],
                "local_dir": "."
            },
            "phi-3-mini": {
                "description": "Phi-3 Mini for better classification (OPTIONAL)",
                "size": "~2.4GB",
                "priority": "OPTIONAL", 
                "base_url": "https://huggingface.co/microsoft/Phi-3-mini-4k-instruct-gguf/resolve/main",
                "files": [
                    "Phi-3-mini-4k-instruct-q4_k_m.gguf"
                ],
                "local_dir": "."
            }
        }
    
    def download_file(self, url: str, local_path: Path, description: str = "") -> bool:
        """Download a single file with progress tracking."""
        try:
            print(f"📥 Downloading {description or local_path.name}...")
            print(f"   URL: {url}")
            print(f"   Path: {local_path}")
            
            # Check if file already exists
            if local_path.exists():
                print(f"   ✅ File already exists, skipping download")
                return True
            
            # Create directory if needed
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Download with progress
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            print(f"\r   Progress: {progress:.1f}% ({downloaded:,}/{total_size:,} bytes)", end='')
            
            print(f"\n   ✅ Downloaded successfully!")
            return True
            
        except Exception as e:
            print(f"\n   ❌ Download failed: {e}")
            if local_path.exists():
                local_path.unlink()  # Remove partial file
            return False
    
    def download_model(self, model_name: str) -> bool:
        """Download all files for a specific model."""
        if model_name not in self.models:
            print(f"❌ Unknown model: {model_name}")
            return False
        
        model_config = self.models[model_name]
        print(f"\n🧠 Downloading {model_name}")
        print(f"   Description: {model_config['description']}")
        print(f"   Size: {model_config['size']}")
        print(f"   Priority: {model_config['priority']}")
        
        # Create model directory
        model_dir = self.models_dir / model_config['local_dir']
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # Download all files
        success_count = 0
        total_files = len(model_config['files'])
        
        for file_name in model_config['files']:
            url = f"{model_config['base_url']}/{file_name}"
            local_path = model_dir / file_name
            
            if self.download_file(url, local_path, f"{model_name}/{file_name}"):
                success_count += 1
            else:
                print(f"   ⚠️ Failed to download {file_name}")
        
        if success_count == total_files:
            print(f"   ✅ {model_name} downloaded successfully!")
            return True
        else:
            print(f"   ⚠️ {model_name} partially downloaded ({success_count}/{total_files} files)")
            return False
    
    def verify_model(self, model_name: str) -> bool:
        """Verify that a model is properly downloaded."""
        if model_name not in self.models:
            return False
        
        model_config = self.models[model_name]
        model_dir = self.models_dir / model_config['local_dir']
        
        for file_name in model_config['files']:
            file_path = model_dir / file_name
            if not file_path.exists():
                return False
        
        return True
    
    def list_models(self):
        """List all available models and their status."""
        print("\n📋 Available Models:")
        print("=" * 80)
        
        for model_name, config in self.models.items():
            status = "✅ DOWNLOADED" if self.verify_model(model_name) else "❌ NOT DOWNLOADED"
            print(f"{model_name:20} | {config['priority']:8} | {config['size']:8} | {status}")
            print(f"   {config['description']}")
            print()
    
    def download_required_models(self):
        """Download all required models for offline operation."""
        print("🚀 Downloading Required Models for Offline Operation")
        print("=" * 60)
        
        required_models = [name for name, config in self.models.items() 
                          if config['priority'] == 'REQUIRED']
        
        success_count = 0
        for model_name in required_models:
            if self.download_model(model_name):
                success_count += 1
        
        print(f"\n📊 Download Summary:")
        print(f"   Required models: {len(required_models)}")
        print(f"   Successfully downloaded: {success_count}")
        
        if success_count == len(required_models):
            print("   ✅ All required models downloaded!")
            return True
        else:
            print("   ⚠️ Some required models failed to download")
            return False
    
    def download_all_models(self):
        """Download all models (required + optional)."""
        print("🚀 Downloading All Models for Maximum Functionality")
        print("=" * 60)
        
        success_count = 0
        total_models = len(self.models)
        
        for model_name in self.models.keys():
            if self.download_model(model_name):
                success_count += 1
        
        print(f"\n📊 Download Summary:")
        print(f"   Total models: {total_models}")
        print(f"   Successfully downloaded: {success_count}")
        
        if success_count == total_models:
            print("   ✅ All models downloaded!")
            return True
        else:
            print(f"   ⚠️ {total_models - success_count} models failed to download")
            return False
    
    def create_test_script(self):
        """Create a test script to verify downloaded models."""
        test_script = """#!/usr/bin/env python3
# Test script for downloaded offline models

import os
from pathlib import Path

def test_nomic_embeddings():
    print("🧪 Testing nomic-embed-text-v1...")
    
    model_path = Path("models/nomic-embed-text")
    required_files = ["config.json", "pytorch_model.bin", "tokenizer.json"]
    
    for file_name in required_files:
        file_path = model_path / file_name
        if file_path.exists():
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name} - MISSING")
            return False
    
    try:
        # Test loading
        from memory.local_embedding_service import LocalEmbeddingService
        service = LocalEmbeddingService()
        
        if service.is_available():
            print("   ✅ Model loads successfully")
            
            # Test embedding generation
            embedding = service.generate_embedding("test text")
            if embedding and len(embedding) == 768:
                print(f"   ✅ Embedding generation works (dimension: {len(embedding)})")
                return True
            else:
                print("   ❌ Embedding generation failed")
                return False
        else:
            print("   ❌ Model failed to load")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    print("🔍 Testing Downloaded Offline Models")
    print("=" * 50)
    
    if test_nomic_embeddings():
        print("\\n✅ All tests passed! Offline system ready.")
    else:
        print("\\n❌ Some tests failed. Check model downloads.")

if __name__ == "__main__":
    main()
"""
        
        test_file = Path("test_offline_models.py")
        with open(test_file, 'w') as f:
            f.write(test_script)
        
        print(f"📝 Created test script: {test_file}")
        print("   Run with: python test_offline_models.py")

def main():
    """Main download interface."""
    downloader = ModelDownloader()
    
    print("🔒 Adrina AI Assistant - Offline Models Downloader")
    print("=" * 60)
    
    while True:
        print("\nOptions:")
        print("1. Download REQUIRED models only (nomic-embed-text-v1)")
        print("2. Download ALL models (required + optional)")
        print("3. List available models and status")
        print("4. Download specific model")
        print("5. Create test script")
        print("6. Exit")
        
        choice = input("\nEnter your choice (1-6): ").strip()
        
        if choice == '1':
            downloader.download_required_models()
            
        elif choice == '2':
            downloader.download_all_models()
            
        elif choice == '3':
            downloader.list_models()
            
        elif choice == '4':
            downloader.list_models()
            model_name = input("\nEnter model name to download: ").strip()
            if model_name in downloader.models:
                downloader.download_model(model_name)
            else:
                print(f"❌ Unknown model: {model_name}")
                
        elif choice == '5':
            downloader.create_test_script()
            
        elif choice == '6':
            print("👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please enter 1-6.")

if __name__ == "__main__":
    main()
