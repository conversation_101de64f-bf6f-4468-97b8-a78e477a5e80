#!/usr/bin/env python3
"""
Universal Logger for Adrina AI Assistant v2.0

Centralized logging system that captures all interactions and data flows
throughout the multi-task AI system including:
- User queries and responses
- Task classification decisions  
- Memory retrieval operations
- Performance metrics
- System health monitoring
- Error tracking and debugging
"""

import os
import sys
import json
import time
import logging
import threading
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

# Create logs directory
LOGS_DIR = Path("./logs")
LOGS_DIR.mkdir(exist_ok=True)

# Import error alert system (avoid circular import)
try:
    from universal_logging.error_alert_system import get_error_alert_system, add_error_alert
except ImportError:
    # Fallback if error alert system not available
    def get_error_alert_system():
        return None
    def add_error_alert(*args, **kwargs):
        return None

class LogLevel(Enum):
    """Log levels for different types of events."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class ComponentType(Enum):
    """System components that generate logs."""
    USER_INTERFACE = "USER_INTERFACE"
    TASK_CLASSIFIER = "TASK_CLASSIFIER"
    MAIN_CHATBOT = "MAIN_CHATBOT"
    MEMORY_SYSTEM = "MEMORY_SYSTEM"
    TTS_SYSTEM = "TTS_SYSTEM"
    CONNECTOR = "CONNECTOR"
    SYSTEM = "SYSTEM"

@dataclass
class LogEntry:
    """Structured log entry for all system events."""
    timestamp: str
    component: str
    level: str
    event_type: str
    message: str
    data: Optional[Dict[str, Any]] = None
    session_id: Optional[str] = None
    query_id: Optional[str] = None
    processing_time: Optional[float] = None
    error_details: Optional[Dict[str, Any]] = None

class UniversalLogger:
    """
    Universal logging system for the entire Adrina AI system.
    Provides centralized logging with structured data capture.
    """
    
    def __init__(self, 
                 log_level: str = "INFO",
                 enable_file_logging: bool = True,
                 enable_console_logging: bool = True,
                 max_log_files: int = 10):
        """Initialize the universal logger."""
        
        self.log_level = getattr(logging, log_level.upper())
        self.enable_file_logging = enable_file_logging
        self.enable_console_logging = enable_console_logging
        self.max_log_files = max_log_files
        self.file_logging_activated = False  # Track if file logging was activated

        # Session tracking
        self.session_id = self._generate_session_id()
        self.query_counter = 0
        self.session_start_time = time.time()
        
        # Performance tracking
        self.performance_metrics = {
            "total_queries": 0,
            "classification_times": [],
            "response_times": [],
            "memory_retrieval_times": [],
            "errors": 0,
            "warnings": 0
        }
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Initialize loggers
        self._setup_loggers()
        
        # Log system startup
        self.log_system_event("Universal Logger initialized", {
            "session_id": self.session_id,
            "log_level": log_level,
            "file_logging": enable_file_logging,
            "console_logging": enable_console_logging
        })
    
    def _generate_session_id(self) -> str:
        """Generate unique session ID."""
        return f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{os.getpid()}"
    
    def _setup_loggers(self):
        """Setup console logger initially. File logging activated on errors."""
        # Main logger
        self.logger = logging.getLogger("AdrinaAI")
        self.logger.setLevel(self.log_level)
        self.logger.handlers.clear()  # Clear existing handlers

        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Console handler (always enabled initially)
        if self.enable_console_logging:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.log_level)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

        # File handlers will be added dynamically when needed
        self.file_handler = None
        self.structured_handler = None
        self.structured_logger = None

    def _activate_file_logging(self):
        """Activate file logging when errors/warnings occur."""
        if self.file_logging_activated:
            return  # Already activated

        try:
            # Formatter
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )

            # Create file handler
            log_file = LOGS_DIR / f"adrina_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            self.file_handler = logging.FileHandler(log_file, encoding='utf-8')
            self.file_handler.setLevel(self.log_level)
            self.file_handler.setFormatter(formatter)
            self.logger.addHandler(self.file_handler)

            # Create structured data logger (JSON format)
            structured_log_file = LOGS_DIR / f"adrina_error_structured_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jsonl"
            self.structured_handler = logging.FileHandler(structured_log_file, encoding='utf-8')
            self.structured_logger = logging.getLogger("AdrinaAI.Structured")
            self.structured_logger.setLevel(logging.DEBUG)
            self.structured_logger.addHandler(self.structured_handler)
            self.structured_logger.propagate = False

            self.file_logging_activated = True

            # Log activation message
            self.logger.warning(f"📁 File logging activated due to error/warning - Logs: {log_file}")

        except Exception as e:
            print(f"❌ Failed to activate file logging: {e}")
    
    def _log_structured(self, entry: LogEntry):
        """Log structured data in JSON format."""
        if self.enable_file_logging:
            try:
                json_entry = json.dumps(asdict(entry), ensure_ascii=False)
                self.structured_logger.info(json_entry)
            except Exception as e:
                self.logger.error(f"Failed to log structured data: {e}")
    
    def _create_log_entry(self, 
                         component: ComponentType,
                         level: LogLevel,
                         event_type: str,
                         message: str,
                         data: Optional[Dict[str, Any]] = None,
                         query_id: Optional[str] = None,
                         processing_time: Optional[float] = None,
                         error_details: Optional[Dict[str, Any]] = None) -> LogEntry:
        """Create a structured log entry."""
        return LogEntry(
            timestamp=datetime.now(timezone.utc).isoformat(),
            component=component.value,
            level=level.value,
            event_type=event_type,
            message=message,
            data=data,
            session_id=self.session_id,
            query_id=query_id,
            processing_time=processing_time,
            error_details=error_details
        )
    
    # ===== USER INTERFACE LOGGING =====
    def log_user_query(self, query: str, query_id: str = None) -> str:
        """Log user input query."""
        with self._lock:
            self.query_counter += 1
            if not query_id:
                query_id = f"query_{self.query_counter:04d}"
            
            self.performance_metrics["total_queries"] += 1
            
            entry = self._create_log_entry(
                ComponentType.USER_INTERFACE,
                LogLevel.INFO,
                "USER_QUERY",
                f"User query received: {query[:100]}{'...' if len(query) > 100 else ''}",
                {"query": query, "query_length": len(query)},
                query_id
            )
            
            self.logger.info(f"🗣️ User Query [{query_id}]: {query}")
            self._log_structured(entry)
            
            return query_id
    
    def log_system_response(self, response: str, query_id: str, processing_time: float = None):
        """Log system response to user."""
        entry = self._create_log_entry(
            ComponentType.USER_INTERFACE,
            LogLevel.INFO,
            "SYSTEM_RESPONSE",
            f"System response: {response[:100]}{'...' if len(response) > 100 else ''}",
            {"response": response, "response_length": len(response)},
            query_id,
            processing_time
        )
        
        self.logger.info(f"🤖 System Response [{query_id}]: {response[:100]}{'...' if len(response) > 100 else ''}")
        self._log_structured(entry)
    
    # ===== TASK CLASSIFICATION LOGGING =====
    def log_classification_start(self, query: str, query_id: str):
        """Log start of query classification."""
        entry = self._create_log_entry(
            ComponentType.TASK_CLASSIFIER,
            LogLevel.DEBUG,
            "CLASSIFICATION_START",
            f"Starting classification for query: {query[:50]}{'...' if len(query) > 50 else ''}",
            {"query": query},
            query_id
        )
        
        self.logger.debug(f"🎯 Classification Start [{query_id}]")
        self._log_structured(entry)
    
    def log_classification_result(self, query_id: str, classification: str, confidence: float, processing_time: float):
        """Log classification result."""
        with self._lock:
            self.performance_metrics["classification_times"].append(processing_time)
        
        entry = self._create_log_entry(
            ComponentType.TASK_CLASSIFIER,
            LogLevel.INFO,
            "CLASSIFICATION_RESULT",
            f"Query classified as {classification} (confidence: {confidence:.2f})",
            {
                "classification": classification,
                "confidence": confidence,
                "processing_time": processing_time
            },
            query_id,
            processing_time
        )
        
        path_icon = "⚡" if classification == "FAST" else "🧠"
        self.logger.info(f"{path_icon} Classification [{query_id}]: {classification} ({processing_time:.3f}s)")
        self._log_structured(entry)
    
    # ===== MAIN CHATBOT LOGGING =====
    def log_chatbot_generation_start(self, query: str, query_id: str, context_provided: bool = False):
        """Log start of response generation."""
        entry = self._create_log_entry(
            ComponentType.MAIN_CHATBOT,
            LogLevel.DEBUG,
            "GENERATION_START",
            f"Starting response generation (context: {'yes' if context_provided else 'no'})",
            {"query": query, "context_provided": context_provided},
            query_id
        )
        
        context_icon = "🧠" if context_provided else "⚡"
        self.logger.debug(f"{context_icon} Generation Start [{query_id}]")
        self._log_structured(entry)
    
    def log_chatbot_generation_complete(self, query_id: str, response: str, processing_time: float):
        """Log completion of response generation."""
        with self._lock:
            self.performance_metrics["response_times"].append(processing_time)
        
        entry = self._create_log_entry(
            ComponentType.MAIN_CHATBOT,
            LogLevel.INFO,
            "GENERATION_COMPLETE",
            f"Response generated ({len(response)} chars)",
            {
                "response_length": len(response),
                "processing_time": processing_time
            },
            query_id,
            processing_time
        )
        
        self.logger.info(f"✅ Generation Complete [{query_id}]: {processing_time:.3f}s")
        self._log_structured(entry)
    
    # ===== MEMORY SYSTEM LOGGING =====
    def log_memory_retrieval_start(self, query: str, query_id: str):
        """Log start of memory retrieval."""
        entry = self._create_log_entry(
            ComponentType.MEMORY_SYSTEM,
            LogLevel.DEBUG,
            "MEMORY_RETRIEVAL_START",
            f"Starting memory retrieval for: {query[:50]}{'...' if len(query) > 50 else ''}",
            {"query": query},
            query_id
        )
        
        self.logger.debug(f"🔍 Memory Retrieval Start [{query_id}]")
        self._log_structured(entry)
    
    def log_memory_retrieval_result(self, query_id: str, results_count: int, processing_time: float, results: List[Dict] = None):
        """Log memory retrieval results."""
        with self._lock:
            self.performance_metrics["memory_retrieval_times"].append(processing_time)
        
        entry = self._create_log_entry(
            ComponentType.MEMORY_SYSTEM,
            LogLevel.INFO,
            "MEMORY_RETRIEVAL_RESULT",
            f"Retrieved {results_count} memories",
            {
                "results_count": results_count,
                "processing_time": processing_time,
                "results": results[:3] if results else None  # Log first 3 results
            },
            query_id,
            processing_time
        )
        
        self.logger.info(f"💾 Memory Retrieved [{query_id}]: {results_count} items ({processing_time:.3f}s)")
        self._log_structured(entry)
    
    def log_memory_storage(self, content: str, memory_type: str, query_id: str = None):
        """Log memory storage operation."""
        entry = self._create_log_entry(
            ComponentType.MEMORY_SYSTEM,
            LogLevel.INFO,
            "MEMORY_STORAGE",
            f"Stored {memory_type} memory ({len(content)} chars)",
            {
                "memory_type": memory_type,
                "content_length": len(content),
                "content_preview": content[:100] + "..." if len(content) > 100 else content
            },
            query_id
        )
        
        self.logger.info(f"💾 Memory Stored [{query_id or 'system'}]: {memory_type}")
        self._log_structured(entry)
    
    # ===== TTS SYSTEM LOGGING =====
    def log_tts_start(self, text: str, query_id: str = None):
        """Log start of TTS processing."""
        entry = self._create_log_entry(
            ComponentType.TTS_SYSTEM,
            LogLevel.DEBUG,
            "TTS_START",
            f"Starting TTS for text ({len(text)} chars)",
            {"text_length": len(text)},
            query_id
        )
        
        self.logger.debug(f"🔊 TTS Start [{query_id or 'system'}]")
        self._log_structured(entry)
    
    def log_tts_complete(self, query_id: str, processing_time: float):
        """Log TTS completion."""
        entry = self._create_log_entry(
            ComponentType.TTS_SYSTEM,
            LogLevel.INFO,
            "TTS_COMPLETE",
            f"TTS processing complete",
            {"processing_time": processing_time},
            query_id,
            processing_time
        )
        
        self.logger.info(f"🔊 TTS Complete [{query_id or 'system'}]: {processing_time:.3f}s")
        self._log_structured(entry)
    
    # ===== SYSTEM EVENTS LOGGING =====
    def log_system_event(self, message: str, data: Dict[str, Any] = None, level: LogLevel = LogLevel.INFO):
        """Log general system events."""
        entry = self._create_log_entry(
            ComponentType.SYSTEM,
            level,
            "SYSTEM_EVENT",
            message,
            data
        )
        
        level_icons = {
            LogLevel.DEBUG: "🔧",
            LogLevel.INFO: "ℹ️",
            LogLevel.WARNING: "⚠️",
            LogLevel.ERROR: "❌",
            LogLevel.CRITICAL: "🚨"
        }
        
        icon = level_icons.get(level, "ℹ️")
        getattr(self.logger, level.value.lower())(f"{icon} {message}")
        self._log_structured(entry)
    
    # ===== ERROR LOGGING =====
    def log_error(self, component: ComponentType, error: Exception, context: str = "", query_id: str = None):
        """Log errors with full context and add to error alert system."""
        # Activate file logging on first error
        if not self.file_logging_activated:
            self._activate_file_logging()

        with self._lock:
            self.performance_metrics["errors"] += 1

        error_details = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context
        }

        entry = self._create_log_entry(
            component,
            LogLevel.ERROR,
            "ERROR",
            f"Error in {component.value}: {str(error)}",
            {"context": context},
            query_id,
            error_details=error_details
        )

        self.logger.error(f"❌ Error [{component.value}] [{query_id or 'system'}]: {error}")
        self._log_structured(entry)

        # Add to error alert system for real-time user notification
        try:
            error_alert_system = get_error_alert_system()
            if error_alert_system:
                error_id = error_alert_system.add_error(
                    component=component.value,
                    error=error,
                    context=context,
                    query_id=query_id
                )
                self.logger.info(f"🚨 Error alert created: {error_id}")
        except Exception as alert_error:
            # Don't let error alert system failures break logging
            self.logger.warning(f"Failed to create error alert: {alert_error}")
    
    def log_warning(self, component: ComponentType, message: str, data: Dict[str, Any] = None, query_id: str = None):
        """Log warnings."""
        # Activate file logging on first warning
        if not self.file_logging_activated:
            self._activate_file_logging()

        with self._lock:
            self.performance_metrics["warnings"] += 1

        entry = self._create_log_entry(
            component,
            LogLevel.WARNING,
            "WARNING",
            message,
            data,
            query_id
        )

        self.logger.warning(f"⚠️ Warning [{component.value}] [{query_id or 'system'}]: {message}")
        self._log_structured(entry)
    
    # ===== PERFORMANCE METRICS =====
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance metrics summary."""
        with self._lock:
            session_duration = time.time() - self.session_start_time
            
            avg_classification_time = (
                sum(self.performance_metrics["classification_times"]) / 
                len(self.performance_metrics["classification_times"])
                if self.performance_metrics["classification_times"] else 0
            )
            
            avg_response_time = (
                sum(self.performance_metrics["response_times"]) / 
                len(self.performance_metrics["response_times"])
                if self.performance_metrics["response_times"] else 0
            )
            
            avg_memory_time = (
                sum(self.performance_metrics["memory_retrieval_times"]) / 
                len(self.performance_metrics["memory_retrieval_times"])
                if self.performance_metrics["memory_retrieval_times"] else 0
            )
            
            return {
                "session_id": self.session_id,
                "session_duration": session_duration,
                "total_queries": self.performance_metrics["total_queries"],
                "avg_classification_time": avg_classification_time,
                "avg_response_time": avg_response_time,
                "avg_memory_retrieval_time": avg_memory_time,
                "total_errors": self.performance_metrics["errors"],
                "total_warnings": self.performance_metrics["warnings"],
                "queries_per_minute": (self.performance_metrics["total_queries"] / (session_duration / 60)) if session_duration > 0 else 0
            }
    
    def log_performance_summary(self):
        """Log current performance summary."""
        summary = self.get_performance_summary()
        self.log_system_event("Performance Summary", summary)
    
    # ===== CLEANUP =====
    def cleanup_old_logs(self):
        """Clean up old log files."""
        try:
            log_files = list(LOGS_DIR.glob("adrina_*.log"))
            log_files.extend(LOGS_DIR.glob("adrina_*.jsonl"))
            
            if len(log_files) > self.max_log_files:
                # Sort by modification time and remove oldest
                log_files.sort(key=lambda x: x.stat().st_mtime)
                for old_file in log_files[:-self.max_log_files]:
                    old_file.unlink()
                    self.log_system_event(f"Cleaned up old log file: {old_file.name}")
        
        except Exception as e:
            self.log_error(ComponentType.SYSTEM, e, "cleanup_old_logs")
    
    def shutdown(self):
        """Shutdown the logger gracefully."""
        if self.enable_console_logging:
            print("🔄 Universal Logger shutting down...")

        # Close file handlers if they were activated
        if self.file_handler:
            self.file_handler.close()

        if self.structured_handler:
            self.structured_handler.close()

        # Close console handlers
        for handler in self.logger.handlers:
            handler.close()

# ===== GLOBAL LOGGER INSTANCE =====
# Create global logger instance
_global_logger = None

def get_logger() -> UniversalLogger:
    """Get the global logger instance."""
    global _global_logger
    if _global_logger is None:
        _global_logger = UniversalLogger()
    return _global_logger

def initialize_logger(log_level: str = "INFO",
                     enable_file_logging: bool = False,  # Only activate on errors
                     enable_console_logging: bool = True) -> UniversalLogger:
    """Initialize the global logger with custom settings."""
    global _global_logger
    _global_logger = UniversalLogger(log_level, enable_file_logging, enable_console_logging)
    return _global_logger

# ===== CONVENIENCE FUNCTIONS =====
def log_user_query(query: str, query_id: str = None) -> str:
    """Convenience function for logging user queries."""
    return get_logger().log_user_query(query, query_id)

def log_system_response(response: str, query_id: str, processing_time: float = None):
    """Convenience function for logging system responses."""
    get_logger().log_system_response(response, query_id, processing_time)

def log_classification_result(query_id: str, classification: str, confidence: float, processing_time: float):
    """Convenience function for logging classification results."""
    get_logger().log_classification_result(query_id, classification, confidence, processing_time)

def log_error(component: ComponentType, error: Exception, context: str = "", query_id: str = None):
    """Convenience function for logging errors."""
    get_logger().log_error(component, error, context, query_id)

def log_system_event(message: str, data: Dict[str, Any] = None, level: LogLevel = LogLevel.INFO):
    """Convenience function for logging system events."""
    get_logger().log_system_event(message, data, level)

# Export main classes and functions
__all__ = [
    'UniversalLogger',
    'LogLevel',
    'ComponentType',
    'get_logger',
    'initialize_logger',
    'log_user_query',
    'log_system_response',
    'log_classification_result',
    'log_error',
    'log_system_event'
]

if __name__ == "__main__":
    # Test the universal logger
    print("🧪 Testing Universal Logger (Error-Triggered File Logging)")
    print("=" * 60)

    logger = initialize_logger("DEBUG")

    print("\n📝 Testing normal operations (console-only)...")
    # Test different log types - these will only go to console
    query_id = logger.log_user_query("What is Python programming?")
    logger.log_classification_result(query_id, "FAST", 0.95, 0.123)
    logger.log_system_response("Python is a programming language...", query_id, 2.573)

    print(f"\n📁 File logging activated: {logger.file_logging_activated}")

    print("\n⚠️ Testing warning (will activate file logging)...")
    logger.log_warning(ComponentType.SYSTEM, "This is a test warning")

    print(f"📁 File logging activated: {logger.file_logging_activated}")

    print("\n❌ Testing error logging...")
    # Test error logging - this will activate file logging if not already active
    try:
        raise ValueError("Test error for demonstration")
    except Exception as e:
        logger.log_error(ComponentType.SYSTEM, e, "Testing error logging", query_id)

    print("\n✅ Universal Logger test completed!")
    if logger.file_logging_activated:
        print(f"📂 Error logs saved to: {LOGS_DIR}")
        print("📝 Check the error log files for detailed information")
    else:
        print("📝 No errors occurred - no log files created")

    logger.shutdown()
