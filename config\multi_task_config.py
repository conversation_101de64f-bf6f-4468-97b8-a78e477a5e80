#!/usr/bin/env python3
"""
Multi-Task Assistant Configuration for Adrina AI v2.0

Configuration settings for the Mistral-7B multi-task assistant
that handles query classification and response enhancement.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass

# ===== MODEL CONFIGURATION =====
# Mistral model path and settings
MISTRAL_MODEL_PATH = os.getenv("MISTRAL_MODEL_PATH", "models/mistral-7b-instruct-v0.2.Q4_K_M.gguf")

# ===== PERFORMANCE SETTINGS =====
# Context and processing settings
CONTEXT_SIZE = int(os.getenv("MISTRAL_CONTEXT_SIZE", "1024"))  # Optimized for classification tasks
CPU_THREADS = int(os.getenv("MISTRAL_CPU_THREADS", "6"))      # Adjust based on your CPU
GPU_LAYERS = int(os.getenv("MISTRAL_GPU_LAYERS", "-1"))       # -1 = use all GPU layers
VERBOSE_MODE = os.getenv("MISTRAL_VERBOSE", "false").lower() == "true"

# ===== CLASSIFICATION SETTINGS =====
# Query classification parameters
CLASSIFICATION_TEMPERATURE = float(os.getenv("CLASSIFICATION_TEMPERATURE", "0.1"))  # Low for consistency
CLASSIFICATION_MAX_TOKENS = int(os.getenv("CLASSIFICATION_MAX_TOKENS", "10"))       # Short responses
CLASSIFICATION_TIMEOUT = int(os.getenv("CLASSIFICATION_TIMEOUT", "30"))            # Seconds

# ===== RESPONSE ENHANCEMENT SETTINGS =====
# Response enhancement parameters
ENHANCEMENT_TEMPERATURE = float(os.getenv("ENHANCEMENT_TEMPERATURE", "0.7"))       # Higher for creativity
ENHANCEMENT_MAX_TOKENS = int(os.getenv("ENHANCEMENT_MAX_TOKENS", "200"))           # Longer responses
ENHANCEMENT_TIMEOUT = int(os.getenv("ENHANCEMENT_TIMEOUT", "60"))                  # Seconds

# ===== QUERY ROUTING CONFIGURATION =====
# Fast vs Memory path settings
FAST_PATH_KEYWORDS = [
    "what is", "how to", "define", "explain", "calculate", "convert",
    "list", "show me", "tell me about", "describe", "compare"
]

MEMORY_PATH_KEYWORDS = [
    "remember", "recall", "what did we", "our previous", "last time",
    "you mentioned", "we discussed", "my preference", "based on our"
]

# Classification confidence thresholds
CLASSIFICATION_CONFIDENCE_THRESHOLD = float(os.getenv("CLASSIFICATION_CONFIDENCE", "0.8"))
FALLBACK_TO_MEMORY = os.getenv("FALLBACK_TO_MEMORY", "true").lower() == "true"

# ===== PERFORMANCE MONITORING =====
# Statistics and monitoring settings
ENABLE_PERFORMANCE_TRACKING = os.getenv("ENABLE_PERFORMANCE_TRACKING", "true").lower() == "true"
MAX_CLASSIFICATION_HISTORY = int(os.getenv("MAX_CLASSIFICATION_HISTORY", "100"))
PERFORMANCE_LOG_INTERVAL = int(os.getenv("PERFORMANCE_LOG_INTERVAL", "10"))  # Log every N queries

# ===== PROMPT TEMPLATES =====
# Classification prompt template
CLASSIFICATION_PROMPT_TEMPLATE = """<s>[INST] You are a query classifier. Classify this query as either "FAST" or "MEMORY".

FAST: Queries that can be answered directly without needing conversation history or stored memories.
Examples: "What is Python?", "How to code?", "Define AI", "What is 2+2?", "Explain machine learning"

MEMORY: Queries that require context from previous conversations or stored information.
Examples: "What did we discuss?", "Remember my preference", "What was my pet's name?", "Based on our previous conversation..."

Query: {query}

Classification: [/INST]"""

# Response enhancement prompt template
ENHANCEMENT_PROMPT_TEMPLATE = """<s>[INST] Enhance this response by incorporating the provided context.

Original Query: {query}

Context from Memory: {context}

Base Response: {base_response}

Enhanced Response: [/INST]"""

# ===== ERROR HANDLING =====
# Error handling and fallback settings
MAX_RETRIES = int(os.getenv("MISTRAL_MAX_RETRIES", "3"))
RETRY_DELAY = float(os.getenv("MISTRAL_RETRY_DELAY", "1.0"))  # Seconds between retries
ENABLE_FALLBACK = os.getenv("ENABLE_FALLBACK", "true").lower() == "true"

# ===== LOGGING CONFIGURATION =====
# Logging settings for multi-task assistant
LOG_LEVEL = os.getenv("MISTRAL_LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = os.getenv("MISTRAL_LOG_FILE", "./logs/multi_task_assistant.log")

# Create logs directory if it doesn't exist
os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)

# ===== DATACLASS CONFIGURATION =====
@dataclass
class MultiTaskConfig:
    """Configuration dataclass for the multi-task assistant."""
    
    # Model settings
    model_path: str = MISTRAL_MODEL_PATH
    n_ctx: int = CONTEXT_SIZE
    n_threads: int = CPU_THREADS
    n_gpu_layers: int = GPU_LAYERS
    verbose: bool = VERBOSE_MODE
    
    # Classification settings
    classification_temperature: float = CLASSIFICATION_TEMPERATURE
    classification_max_tokens: int = CLASSIFICATION_MAX_TOKENS
    classification_timeout: int = CLASSIFICATION_TIMEOUT
    
    # Enhancement settings
    enhancement_temperature: float = ENHANCEMENT_TEMPERATURE
    enhancement_max_tokens: int = ENHANCEMENT_MAX_TOKENS
    enhancement_timeout: int = ENHANCEMENT_TIMEOUT
    
    # Performance settings
    enable_performance_tracking: bool = ENABLE_PERFORMANCE_TRACKING
    max_classification_history: int = MAX_CLASSIFICATION_HISTORY
    
    # Error handling
    max_retries: int = MAX_RETRIES
    retry_delay: float = RETRY_DELAY
    enable_fallback: bool = ENABLE_FALLBACK
    
    # Prompt templates
    classification_prompt: str = CLASSIFICATION_PROMPT_TEMPLATE
    enhancement_prompt: str = ENHANCEMENT_PROMPT_TEMPLATE

# ===== CONFIGURATION VALIDATION =====
def validate_multi_task_config() -> bool:
    """Validate multi-task configuration settings."""
    try:
        # Check model file exists
        if not os.path.exists(MISTRAL_MODEL_PATH):
            print(f"❌ Mistral model not found: {MISTRAL_MODEL_PATH}")
            return False
        
        # Validate numeric ranges
        assert 512 <= CONTEXT_SIZE <= 4096, f"Context size must be between 512-4096, got {CONTEXT_SIZE}"
        assert 1 <= CPU_THREADS <= 32, f"CPU threads must be between 1-32, got {CPU_THREADS}"
        assert 0.0 <= CLASSIFICATION_TEMPERATURE <= 2.0, f"Classification temperature must be 0.0-2.0"
        assert 0.0 <= ENHANCEMENT_TEMPERATURE <= 2.0, f"Enhancement temperature must be 0.0-2.0"
        assert 1 <= CLASSIFICATION_MAX_TOKENS <= 100, f"Classification max tokens must be 1-100"
        assert 10 <= ENHANCEMENT_MAX_TOKENS <= 1000, f"Enhancement max tokens must be 10-1000"
        
        # Validate timeouts
        assert 5 <= CLASSIFICATION_TIMEOUT <= 300, f"Classification timeout must be 5-300 seconds"
        assert 10 <= ENHANCEMENT_TIMEOUT <= 600, f"Enhancement timeout must be 10-600 seconds"
        
        print("✅ Multi-task configuration validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Multi-task configuration validation failed: {e}")
        return False

# ===== CONFIGURATION SUMMARY =====
def get_config_summary() -> Dict[str, Any]:
    """Get a summary of the current configuration."""
    return {
        "model": {
            "path": MISTRAL_MODEL_PATH,
            "context_size": CONTEXT_SIZE,
            "gpu_layers": GPU_LAYERS,
            "threads": CPU_THREADS
        },
        "classification": {
            "temperature": CLASSIFICATION_TEMPERATURE,
            "max_tokens": CLASSIFICATION_MAX_TOKENS,
            "timeout": CLASSIFICATION_TIMEOUT
        },
        "enhancement": {
            "temperature": ENHANCEMENT_TEMPERATURE,
            "max_tokens": ENHANCEMENT_MAX_TOKENS,
            "timeout": ENHANCEMENT_TIMEOUT
        },
        "performance": {
            "tracking_enabled": ENABLE_PERFORMANCE_TRACKING,
            "max_history": MAX_CLASSIFICATION_HISTORY,
            "log_interval": PERFORMANCE_LOG_INTERVAL
        },
        "error_handling": {
            "max_retries": MAX_RETRIES,
            "retry_delay": RETRY_DELAY,
            "fallback_enabled": ENABLE_FALLBACK
        }
    }

# ===== DEFAULT CONFIGURATION INSTANCE =====
# Create default configuration instance
DEFAULT_CONFIG = MultiTaskConfig()

# Export commonly used configurations
__all__ = [
    'MultiTaskConfig',
    'DEFAULT_CONFIG',
    'MISTRAL_MODEL_PATH',
    'CONTEXT_SIZE',
    'CPU_THREADS',
    'GPU_LAYERS',
    'CLASSIFICATION_TEMPERATURE',
    'ENHANCEMENT_TEMPERATURE',
    'CLASSIFICATION_MAX_TOKENS',
    'ENHANCEMENT_MAX_TOKENS',
    'CLASSIFICATION_PROMPT_TEMPLATE',
    'ENHANCEMENT_PROMPT_TEMPLATE',
    'validate_multi_task_config',
    'get_config_summary'
]

# ===== CONFIGURATION TESTING =====
if __name__ == "__main__":
    print("🔧 Multi-Task Assistant Configuration")
    print("=" * 50)
    
    # Validate configuration
    if validate_multi_task_config():
        print("\n📊 Configuration Summary:")
        summary = get_config_summary()
        
        for category, settings in summary.items():
            print(f"\n{category.upper()}:")
            for key, value in settings.items():
                print(f"  {key}: {value}")
        
        print(f"\n✅ Multi-task configuration ready!")
        print(f"📂 Model: {MISTRAL_MODEL_PATH}")
        print(f"🎯 Classification Temperature: {CLASSIFICATION_TEMPERATURE}")
        print(f"⚡ GPU Layers: {GPU_LAYERS}")
        
    else:
        print("\n❌ Configuration validation failed!")
        print("Please check your settings and try again.")
