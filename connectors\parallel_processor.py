#!/usr/bin/env python3
"""
Parallel Processing Manager for Adrina AI Assistant v2.0

Implements parallel architecture where:
- Main chatbot responds immediately for fast user experience
- Task assistant works in background for intelligence and context
- Handles uncensored responses when censored LLM refuses
"""

import os
import sys
import threading
import time
import queue
from typing import Dict, Optional, Callable, Any
from queue import Queue
from dataclasses import dataclass
from enum import Enum

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from universal_logging.universal_logger import ComponentType, get_logger
from chatbot.query_identifier import QueryIdentifier, QueryType, ProcessingPath, MemoryAction
from voice.streaming_tts_handler import StreamingTTSHandler

class ResponseState(Enum):
    """States of response processing."""
    STARTING = "starting"
    STREAMING = "streaming"
    ENHANCING = "enhancing"
    COMPLETE = "complete"
    ERROR = "error"

class CensorshipAction(Enum):
    """Actions to take when censorship is detected."""
    PROCEED_UNCENSORED = "proceed_uncensored"
    ENHANCE_RESPONSE = "enhance_response"
    PROVIDE_CONTEXT = "provide_context"
    NO_ACTION = "no_action"

@dataclass
class ResponseContext:
    """Context information for response processing."""
    query_id: str
    user_query: str
    response_state: ResponseState
    main_response: str = ""
    background_analysis: Optional[Dict] = None
    memory_context: Optional[str] = None
    censorship_detected: bool = False
    censorship_action: CensorshipAction = CensorshipAction.NO_ACTION
    processing_times: Dict[str, float] = None
    
    def __post_init__(self):
        if self.processing_times is None:
            self.processing_times = {}

class ParallelProcessor:
    """
    Manages parallel processing of user queries with immediate main chatbot response
    and background task assistant intelligence.
    """
    
    def __init__(self, main_chatbot, task_assistant, memory_connector=None, tts_manager=None):
        self.main_chatbot = main_chatbot
        self.task_assistant = task_assistant
        self.memory_connector = memory_connector
        self.query_identifier = QueryIdentifier()

        # Initialize streaming TTS handler
        self.streaming_tts_handler = StreamingTTSHandler(tts_manager) if tts_manager else None

        # Logging
        self.logger = get_logger()
        
        # Background processing
        self.background_queue = Queue()
        self.response_contexts = {}  # Store active response contexts
        self.background_thread = None
        self.shutdown_event = threading.Event()
        
        # Censorship detection patterns
        self.censorship_patterns = [
            "i can't", "i cannot", "i'm not able", "i'm unable",
            "i don't feel comfortable", "i can't help with that",
            "that's not appropriate", "i'm not programmed",
            "i'm designed to be helpful", "i must decline",
            "i can't assist with", "i'm not allowed",
            "against my guidelines", "inappropriate request"
        ]
        
        # Start background processor
        self._start_background_processor()
    
    def _start_background_processor(self):
        """Start the background processing thread."""
        self.background_thread = threading.Thread(
            target=self._background_worker,
            daemon=True,
            name="BackgroundProcessor"
        )
        self.background_thread.start()
        self.logger.log_system_event("🧠 Background processor started")
    
    def _background_worker(self):
        """Background worker that processes intelligence tasks."""
        while not self.shutdown_event.is_set():
            try:
                # Get task from queue with timeout
                task = self.background_queue.get(timeout=1.0)
                if task is None:  # Shutdown signal
                    break
                
                # Process the background task
                self._process_background_task(task)
                self.background_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "background processing")
    
    def _process_background_task(self, context: ResponseContext):
        """Process background intelligence task."""
        try:
            start_time = time.time()
            
            # Step 1: Advanced query identification
            self._analyze_query(context)
            
            # Step 2: Check for censorship in main response
            self._check_censorship(context)
            
            # Step 3: Fetch memory context if needed
            self._fetch_memory_context(context)
            
            # Step 4: Enhance response if possible
            self._enhance_response(context)
            
            context.processing_times['background_total'] = time.time() - start_time
            context.response_state = ResponseState.COMPLETE
            
            self.logger.log_system_event(
                f"Background processing completed for query {context.query_id}",
                {
                    "processing_time": context.processing_times['background_total'],
                    "censorship_detected": context.censorship_detected,
                    "memory_used": context.memory_context is not None
                }
            )
            
        except Exception as e:
            context.response_state = ResponseState.ERROR
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, f"background task for {context.query_id}")
    
    def _analyze_query(self, context: ResponseContext):
        """Analyze query using task assistant and query identifier."""
        try:
            analysis_start = time.time()
            
            # Use query identifier for detailed analysis
            classification = self.query_identifier.identify_query(context.user_query)
            
            # Try to get task assistant classification (may be censored)
            task_classification = None
            if self.task_assistant and self.task_assistant.is_loaded:
                try:
                    task_result = self.task_assistant.classify_query(context.user_query, context.query_id)
                    task_classification = {
                        "query_type": task_result.query_type,
                        "confidence": task_result.confidence,
                        "reasoning": task_result.reasoning
                    }
                except Exception as e:
                    self.logger.log_warning(ComponentType.TASK_CLASSIFIER, f"Task assistant classification failed: {e}")
            
            context.background_analysis = {
                "advanced_classification": {
                    "query_type": classification.query_type.value,
                    "processing_path": classification.processing_path.value,
                    "memory_action": classification.memory_action.value,
                    "context_keywords": classification.context_keywords,
                    "priority_level": classification.priority_level,
                    "confidence": classification.confidence
                },
                "task_classification": task_classification,
                "analysis_time": time.time() - analysis_start
            }
            
        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, f"query analysis for {context.query_id}")
    
    def _check_censorship(self, context: ResponseContext):
        """Check if main response shows signs of censorship and determine action."""
        if not context.main_response:
            return
        
        response_lower = context.main_response.lower()
        
        # Check for censorship patterns
        for pattern in self.censorship_patterns:
            if pattern in response_lower:
                context.censorship_detected = True
                break
        
        if context.censorship_detected:
            # Determine appropriate action based on query analysis
            if context.background_analysis:
                query_type = context.background_analysis["advanced_classification"]["query_type"]
                priority = context.background_analysis["advanced_classification"]["priority_level"]
                
                if priority >= 2 or query_type in ["task_execution", "general_chat"]:
                    context.censorship_action = CensorshipAction.PROCEED_UNCENSORED
                else:
                    context.censorship_action = CensorshipAction.ENHANCE_RESPONSE
            else:
                context.censorship_action = CensorshipAction.PROCEED_UNCENSORED
            
            self.logger.log_system_event(
                f"Censorship detected in response for {context.query_id}",
                {
                    "action": context.censorship_action.value,
                    "response_preview": context.main_response[:100]
                }
            )
    
    def _fetch_memory_context(self, context: ResponseContext):
        """Fetch memory context if needed."""
        if not self.memory_connector or not context.background_analysis:
            return
        
        try:
            memory_action = context.background_analysis["advanced_classification"]["memory_action"]
            
            if memory_action in ["retrieve_only", "store_context"]:
                memory_start = time.time()
                
                # Search for relevant memories
                memories = self.memory_connector.search_memories(
                    context.user_query,
                    max_results=3
                )
                
                if memories:
                    context.memory_context = "\n".join([
                        f"- {memory.get('content', '')}" for memory in memories
                    ])
                    
                    context.processing_times['memory_retrieval'] = time.time() - memory_start
                    
                    self.logger.log_system_event(
                        f"Memory context retrieved for {context.query_id}",
                        {"memories_found": len(memories)}
                    )
        
        except Exception as e:
            self.logger.log_error(ComponentType.MEMORY_SYSTEM, e, f"memory retrieval for {context.query_id}")
    
    def _enhance_response(self, context: ResponseContext):
        """Enhance response based on background analysis."""
        try:
            if context.censorship_detected and context.censorship_action == CensorshipAction.PROCEED_UNCENSORED:
                # Signal that uncensored response should be provided
                # This will be handled by the response callback
                pass
            
            elif context.memory_context:
                # Memory context available for enhancement
                # This could be used for follow-up responses or context injection
                pass
            
        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, f"response enhancement for {context.query_id}")
    
    def process_query_parallel(self, user_query: str, response_callback: Callable[[str], None]) -> ResponseContext:
        """
        Process query with parallel architecture.
        
        Args:
            user_query: User's input query
            response_callback: Callback function to handle response chunks
            
        Returns:
            ResponseContext with processing information
        """
        # Generate unique query ID
        query_id = f"query_{int(time.time() * 1000) % 100000:05d}"
        
        # Create response context
        context = ResponseContext(
            query_id=query_id,
            user_query=user_query,
            response_state=ResponseState.STARTING
        )
        
        # Store context for background processing
        self.response_contexts[query_id] = context
        
        # Log user query
        self.logger.log_user_query(user_query, query_id)
        
        # Start immediate main chatbot response
        main_thread = threading.Thread(
            target=self._process_main_response,
            args=(context, response_callback),
            daemon=True,
            name=f"MainResponse-{query_id}"
        )
        main_thread.start()
        
        # Queue background processing
        self.background_queue.put(context)
        
        return context
    
    def _process_main_response(self, context: ResponseContext, response_callback: Callable[[str], None]):
        """Process main chatbot response immediately with streaming TTS."""
        try:
            context.response_state = ResponseState.STREAMING
            main_start = time.time()

            # Initialize streaming TTS for this response
            streaming_tts = None
            if self.streaming_tts_handler:
                streaming_tts = self.streaming_tts_handler.start_streaming_response(context.query_id)
                self.logger.log_system_event(f"🎤 Started streaming TTS for {context.query_id}")

            # Generate streaming response from main chatbot
            full_response = ""

            if hasattr(self.main_chatbot, 'generate_response_stream'):
                # Streaming response with real-time TTS
                for chunk in self.main_chatbot.generate_response_stream(context.user_query):
                    if chunk:
                        full_response += chunk
                        response_callback(chunk)

                        # Add to streaming TTS immediately
                        if streaming_tts:
                            streaming_tts.add_text(chunk)
            else:
                # Non-streaming response
                full_response = self.main_chatbot.generate_response(context.user_query)
                response_callback(full_response)

                # Add entire response to streaming TTS
                if streaming_tts:
                    streaming_tts.add_text(full_response)
            
            context.main_response = full_response
            context.processing_times['main_response'] = time.time() - main_start
            
            # Log main response
            self.logger.log_system_response(full_response, context.query_id, context.processing_times['main_response'])

            # Complete streaming TTS
            if streaming_tts:
                streaming_tts.finalize()
                self.streaming_tts_handler.complete_streaming_response(context.query_id)
                self.logger.log_system_event(f"🎤 Completed streaming TTS for {context.query_id}")

        except Exception as e:
            context.response_state = ResponseState.ERROR
            error_msg = f"Error generating response: {e}"
            context.main_response = error_msg
            response_callback(error_msg)

            # Cancel streaming TTS on error
            if streaming_tts:
                streaming_tts.finalize()
                self.streaming_tts_handler.complete_streaming_response(context.query_id)
                self.logger.log_system_event(f"🎤 Cancelled streaming TTS for {context.query_id} due to error")

            self.logger.log_error(ComponentType.MAIN_CHATBOT, e, f"main response for {context.query_id}")
    
    def get_context(self, query_id: str) -> Optional[ResponseContext]:
        """Get response context by query ID."""
        return self.response_contexts.get(query_id)
    
    def cleanup_context(self, query_id: str):
        """Clean up response context."""
        if query_id in self.response_contexts:
            del self.response_contexts[query_id]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processor statistics."""
        active_contexts = len(self.response_contexts)
        
        return {
            "active_contexts": active_contexts,
            "background_queue_size": self.background_queue.qsize(),
            "background_thread_alive": self.background_thread.is_alive() if self.background_thread else False,
            "total_processed": len([c for c in self.response_contexts.values() if c.response_state == ResponseState.COMPLETE])
        }
    
    def shutdown(self):
        """Shutdown the parallel processor."""
        self.logger.log_system_event("🔄 Shutting down parallel processor...")
        
        # Signal shutdown
        self.shutdown_event.set()
        self.background_queue.put(None)
        
        # Wait for background thread
        if self.background_thread and self.background_thread.is_alive():
            self.background_thread.join(timeout=5)
        
        self.logger.log_system_event("✅ Parallel processor shutdown complete")

# Test function
def test_parallel_processor():
    """Test the parallel processor."""
    print("🧪 Testing Parallel Processor")
    print("=" * 50)
    
    # Mock chatbot for testing
    class MockChatbot:
        def generate_response_stream(self, query):
            response = f"This is a test response to: {query}"
            for word in response.split():
                yield word + " "
                time.sleep(0.1)  # Simulate streaming
    
    # Mock task assistant
    class MockTaskAssistant:
        def __init__(self):
            self.is_loaded = True
        
        def classify_query(self, query, query_id=None):
            from chatbot.multi_task_assistant import QueryClassification
            return QueryClassification("FAST", 0.9, "Test classification", 0.1)
    
    processor = ParallelProcessor(MockChatbot(), MockTaskAssistant())
    
    def response_handler(chunk):
        print(chunk, end="", flush=True)
    
    print("\n🚀 Processing test query...")
    context = processor.process_query_parallel("What is Python?", response_handler)
    
    # Wait for processing to complete
    time.sleep(3)
    
    print(f"\n\n📊 Final context state: {context.response_state}")
    print(f"🧠 Background analysis: {context.background_analysis is not None}")
    print(f"🚫 Censorship detected: {context.censorship_detected}")
    
    processor.shutdown()
    print("\n✅ Parallel processor test completed!")

if __name__ == "__main__":
    test_parallel_processor()
