# streaming_tts.py

import numpy as np
import pyaudio
import threading
import warnings
from kokoro import KPipeline
from queue import Queue, Empty  # <--- FIX: Imported Empty here
from config.main_chatbot_config import (
    TTS_REPO_ID, TTS_LANG_CODE, TTS_VOICE, TTS_SPEED,
    TTS_SAMPLE_RATE, TTS_FRAMES_PER_BUFFER
)

# Suppress some warnings
warnings.filterwarnings('ignore', message='dropout option adds dropout after all but last recurrent layer.*')
warnings.filterwarnings('ignore', message='.*torch.nn.utils.weight_norm.*is deprecated.*')

class TTSManager:
    """Manages the TTS pipeline and audio playback for streaming."""
    def __init__(self):
        self.pipeline = KPipeline(lang_code=TTS_LANG_CODE, repo_id=TTS_REPO_ID)
        self.audio_queue = Queue()
        self._stop_event = threading.Event()

        self.p = pyaudio.PyAudio()
        self.stream = self.p.open(
            format=pyaudio.paFloat32,
            channels=1,
            rate=TTS_SAMPLE_RATE,
            output=True,
            frames_per_buffer=TTS_FRAMES_PER_BUFFER
        )
        
        self.consumer_thread = threading.Thread(target=self._audio_consumer)
        self.consumer_thread.daemon = True
        self.consumer_thread.start()

    def _audio_consumer(self):
        """Consumes audio from the queue and plays it."""
        while not self._stop_event.is_set():
            try:
                audio_chunk = self.audio_queue.get(timeout=0.1)
                if audio_chunk is None:
                    continue
                self.stream.write(audio_chunk.tobytes())
                self.audio_queue.task_done()
            except Empty: # This will now work correctly
                continue

    def speak(self, text):
        """Generates audio from text and puts it in the queue."""
        if not text.strip():
            return

        import time

        # This print statement is useful for debugging
        # print(f"[TTS] Generating audio for: '{text}'")

        print(f"🎤 TTS: Starting audio generation for {len(text)} characters...")
        generation_start = time.time()

        audio_chunks = []
        for _, _, audio in self.pipeline(text, voice=TTS_VOICE, speed=TTS_SPEED):
            if isinstance(audio, np.ndarray):
                audio_chunks.append(audio.astype(np.float32))
            else:
                audio_chunks.append(np.array(audio, dtype=np.float32))

        generation_time = time.time() - generation_start
        print(f"🎵 TTS: Audio generation completed in {generation_time:.2f}s")

        if audio_chunks:
            queue_start = time.time()
            full_audio = np.concatenate(audio_chunks)
            self.audio_queue.put(full_audio)
            queue_time = time.time() - queue_start
            print(f"🔊 TTS: Audio queued for playback in {queue_time:.3f}s")

    def stop(self):
        """Stops the audio playback and cleans up resources."""
        self._stop_event.set()
        if self.consumer_thread.is_alive():
            self.consumer_thread.join()
        self.stream.stop_stream()
        self.stream.close()
        self.p.terminate()