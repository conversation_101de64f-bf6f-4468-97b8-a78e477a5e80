#!/usr/bin/env python3
"""
Command Response Handler for Adrina AI Assistant v2.0

Generates appropriate responses for understood commands, including:
- Immediate acknowledgment of command understanding
- Task plan presentation to user
- Progress updates and status reports
- Confirmation requests for complex operations
"""

import os
import sys
from typing import Dict, List, Optional, Any
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from universal_logging.universal_logger import ComponentType, get_logger

class CommandResponseHandler:
    """Handles generating responses for understood commands."""
    
    def __init__(self):
        self.logger = get_logger()
        
        # Response templates for different command types
        self.response_templates = {
            'direct_command': "I understand you want to {action} {targets}. {task_info}",
            'priority_command': "🔥 {priority_level} priority command received: {action} {targets}. {task_info}",
            'task_command': "Task command understood: {action} {targets}. {task_info}",
            'query_command': "I'll help you {action} {targets}. {task_info}",
            'system_command': "System command: {action} {targets}. {task_info}",
            'conversational': "I understand you're asking about {targets}. {task_info}"
        }
        
        # Priority level descriptions
        self.priority_descriptions = {
            1: "CRITICAL",
            2: "HIGH",
            3: "MEDIUM", 
            4: "LOW",
            5: "BACKGROUND"
        }
        
        # Action descriptions
        self.action_descriptions = {
            'create': 'create',
            'modify': 'update',
            'delete': 'remove',
            'analyze': 'analyze',
            'fix': 'fix',
            'optimize': 'optimize',
            'document': 'document',
            'test': 'test',
            'configure': 'configure',
            'search': 'find'
        }
    
    def generate_command_response(self, command_analysis: Dict[str, Any]) -> str:
        """
        Generate an appropriate response for a command analysis.
        
        Args:
            command_analysis: Command analysis from MultiTaskAssistant.understand_command()
            
        Returns:
            Human-readable response string
        """
        try:
            if not command_analysis.get('command_understood', False):
                return self._generate_error_response(command_analysis)
            
            intent = command_analysis['intent']
            command_type = intent['command_type']
            action_type = intent['action_type']
            priority = intent.get('priority_numeric', 3)
            targets = command_analysis.get('targets', [])
            task_plan = command_analysis.get('task_plan')
            
            # Generate main response
            response_parts = []
            
            # 1. Command acknowledgment
            acknowledgment = self._generate_acknowledgment(command_type, action_type, priority, targets)
            response_parts.append(acknowledgment)
            
            # 2. Task plan information (if available)
            if task_plan:
                task_info = self._generate_task_plan_info(task_plan)
                response_parts.append(task_info)
            
            # 3. Next steps or confirmation request
            if command_analysis.get('requires_confirmation', False):
                confirmation = self._generate_confirmation_request(command_analysis)
                response_parts.append(confirmation)
            elif command_analysis.get('immediate_action', False):
                action_info = self._generate_immediate_action_info(command_analysis)
                response_parts.append(action_info)
            else:
                next_steps = self._generate_next_steps_info(command_analysis)
                response_parts.append(next_steps)
            
            return "\n\n".join(response_parts)
            
        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "command response generation")
            return f"I understood your command but encountered an issue generating a response: {e}"
    
    def _generate_error_response(self, command_analysis: Dict[str, Any]) -> str:
        """Generate response for failed command understanding."""
        error = command_analysis.get('error', 'Unknown error')
        fallback = command_analysis.get('fallback_suggestion', 'Please try rephrasing your request')
        
        return f"I had trouble understanding your command: {error}\n\n💡 Suggestion: {fallback}"
    
    def _generate_acknowledgment(self, command_type: str, action_type: str, priority: int, targets: List[str]) -> str:
        """Generate command acknowledgment."""
        # Format targets
        targets_str = self._format_targets(targets)
        
        # Get action description
        action_desc = self.action_descriptions.get(action_type, action_type)
        
        # Get priority description
        priority_desc = self.priority_descriptions.get(priority, "MEDIUM")
        
        # Generate acknowledgment based on command type
        if command_type == 'priority_command':
            if priority <= 2:  # Critical or High
                return f"🚨 **{priority_desc} PRIORITY** command received!\n🎯 I'll {action_desc} {targets_str} immediately."
            else:
                return f"📋 **{priority_desc} priority** task noted.\n🎯 I'll {action_desc} {targets_str}."
        
        elif command_type == 'direct_command':
            return f"✅ **Command understood**: {action_desc} {targets_str}"
        
        elif command_type == 'task_command':
            return f"📋 **Task command**: I'll {action_desc} {targets_str}"
        
        elif command_type == 'system_command':
            return f"⚙️ **System command**: {action_desc} {targets_str}"
        
        elif command_type == 'query_command':
            return f"🔍 **Query**: I'll help you {action_desc} {targets_str}"
        
        else:
            return f"💬 I understand you want to {action_desc} {targets_str}"
    
    def _format_targets(self, targets: List[str]) -> str:
        """Format target objects for display."""
        if not targets:
            return "the requested items"
        
        # Clean up targets (remove common words)
        cleaned_targets = [t for t in targets if t not in ['the', 'a', 'an', 'for', 'to', 'in', 'on', 'at']]
        
        if not cleaned_targets:
            return "the requested items"
        
        if len(cleaned_targets) == 1:
            return cleaned_targets[0]
        elif len(cleaned_targets) == 2:
            return f"{cleaned_targets[0]} and {cleaned_targets[1]}"
        else:
            return f"{', '.join(cleaned_targets[:-1])}, and {cleaned_targets[-1]}"
    
    def _generate_task_plan_info(self, task_plan: Dict[str, Any]) -> str:
        """Generate task plan information."""
        total_steps = task_plan['total_steps']
        complexity = task_plan['complexity_level']
        duration = task_plan['estimated_duration']
        objective = task_plan['primary_objective']
        
        info_parts = [
            f"📋 **Task Plan Created**:",
            f"   🎯 **Objective**: {objective}",
            f"   📊 **Complexity**: {complexity.title()}",
            f"   ⏱️ **Estimated Time**: {duration}",
            f"   📝 **Steps**: {total_steps} tasks identified"
        ]
        
        # Add first few steps preview
        steps = task_plan.get('steps', [])
        if steps:
            info_parts.append("\n📝 **Next Steps**:")
            for i, step in enumerate(steps[:3], 1):  # Show first 3 steps
                info_parts.append(f"   {i}. {step['description']}")
            
            if len(steps) > 3:
                info_parts.append(f"   ... and {len(steps) - 3} more steps")
        
        # Add risk factors if any
        risks = task_plan.get('risk_factors', [])
        if risks:
            info_parts.append(f"\n⚠️ **Considerations**: {', '.join(risks[:2])}")
        
        return "\n".join(info_parts)
    
    def _generate_confirmation_request(self, command_analysis: Dict[str, Any]) -> str:
        """Generate confirmation request for complex operations."""
        intent = command_analysis['intent']
        priority = intent.get('priority_numeric', 3)
        
        if priority <= 2:  # Critical or High priority
            return "⚡ **Ready to proceed immediately**. This is high priority - shall I start now?"
        else:
            return "🤔 **Confirmation needed**: This is a complex operation. Would you like me to proceed with this plan?"
    
    def _generate_immediate_action_info(self, command_analysis: Dict[str, Any]) -> str:
        """Generate information for immediate actions."""
        return "⚡ **Starting immediately** - I'll begin working on this right away and keep you updated on progress."
    
    def _generate_next_steps_info(self, command_analysis: Dict[str, Any]) -> str:
        """Generate next steps information."""
        intent = command_analysis['intent']
        priority = intent.get('priority_numeric', 3)
        
        if priority <= 2:
            return "🚀 **Next**: I'll prioritize this task and begin implementation. You'll see progress updates as I work."
        elif priority == 3:
            return "📋 **Next**: I'll add this to the task queue and work on it systematically. I'll update you when complete."
        else:
            return "📝 **Next**: I've noted this task and will work on it when time permits. You can check progress anytime."
    
    def generate_task_progress_update(self, task_id: str, completed_steps: int, total_steps: int, current_step: str) -> str:
        """Generate a progress update for an ongoing task."""
        progress_percent = (completed_steps / total_steps) * 100
        progress_bar = "█" * int(progress_percent // 10) + "░" * (10 - int(progress_percent // 10))
        
        return f"""🔄 **Task Progress Update** (ID: {task_id})
📊 Progress: [{progress_bar}] {progress_percent:.0f}% ({completed_steps}/{total_steps} steps)
⚡ Current: {current_step}"""
    
    def generate_task_completion_response(self, task_id: str, objective: str, duration: str) -> str:
        """Generate response for completed task."""
        return f"""✅ **Task Completed Successfully!**
🎯 **Objective**: {objective}
⏱️ **Duration**: {duration}
🆔 **Task ID**: {task_id}

The task has been completed as requested. All steps were executed successfully."""

# Test function
def test_command_response_handler():
    """Test the command response handler."""
    handler = CommandResponseHandler()
    
    # Test command analysis (simulated)
    test_analysis = {
        'command_id': 'cmd_12345',
        'command_understood': True,
        'intent': {
            'command_type': 'priority_command',
            'action_type': 'configure',
            'priority': 'CRITICAL',
            'priority_numeric': 1,
            'confidence': 0.95,
            'reasoning': 'High priority configuration command'
        },
        'targets': ['assistant', 'command understanding'],
        'context_scope': 'system_wide',
        'urgency_indicators': ['urgent'],
        'task_plan': {
            'task_id': 'task_67890',
            'primary_objective': 'Configure assistant for command understanding',
            'total_steps': 6,
            'estimated_duration': '4-6 hours',
            'complexity_level': 'complex',
            'steps': [
                {
                    'step_number': 1,
                    'description': 'Design command recognition patterns',
                    'action_type': 'analyze',
                    'estimated_effort': 'medium',
                    'target_files': ['chatbot/command_processor.py'],
                    'success_criteria': 'Patterns defined for all command types'
                },
                {
                    'step_number': 2,
                    'description': 'Implement priority detection system',
                    'action_type': 'create',
                    'estimated_effort': 'medium',
                    'target_files': ['chatbot/command_processor.py'],
                    'success_criteria': 'Priority levels correctly identified'
                }
            ],
            'required_tools': ['Python', 'regex', 'testing framework'],
            'risk_factors': ['Complex integration', 'Performance impact']
        },
        'immediate_action': False,
        'requires_confirmation': True
    }
    
    print("🧪 Testing Command Response Handler")
    print("=" * 50)
    
    response = handler.generate_command_response(test_analysis)
    print("📝 Generated Response:")
    print("-" * 30)
    print(response)
    
    print("\n" + "=" * 50)
    print("✅ Command response handler test completed!")

if __name__ == "__main__":
    test_command_response_handler()
