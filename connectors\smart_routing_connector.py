#!/usr/bin/env python3
"""
Smart Routing Connector for Adrina AI Assistant v2.0

Intelligent query routing using tiny LLM classifier:
- Tiny LLM: Ultra-fast query classification (0.1-0.5s)
- Main LLM: Primary conversation and responses  
- Mistral LLM: Deep research and complex analysis only

Replaces parallel processing with smart conditional routing.
"""

import os
import sys
import time
import threading
from typing import Dict, Optional
from queue import Queue

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from chatbot.tiny_llm_classifier import TinyLLMClassifier, QueryCategory
from connectors.chatbot_memory_connector import ChatbotMemoryConnector
from chatbot.multi_task_assistant import MultiTaskAssistant
from voice.kokoro_tts import TTSManager
from voice.streaming_tts_handler import StreamingTTSHandler
from universal_logging.universal_logger import ComponentType, get_logger

class SmartRoutingConnector:
    """
    Smart routing connector with three-tier LLM architecture:
    
    1. Tiny LLM: Ultra-fast classification (0.1-0.5s)
    2. Main LLM: Primary responses (Lexi-Llama)
    3. Mistral LLM: Deep research only (when needed)
    """
    
    def __init__(self):
        self.logger = get_logger()
        
        # Core components
        self.tiny_classifier = None
        self.chatbot_memory_connector = None
        self.mistral_brain = None
        self.tts_manager = None
        self.streaming_tts_handler = None
        
        # TTS and speech
        self.speech_queue = Queue()
        self.speech_thread = None
        self.is_speaking = False
        
        # Statistics
        self.stats = {
            'total_queries': 0,
            'simple_queries': 0,
            'complex_queries': 0, 
            'research_queries': 0,
            'avg_classification_time': 0.0,
            'avg_response_time': 0.0
        }
        
        # Initialize system
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize all system components."""
        try:
            self.logger.log_system_event("🚀 Initializing Smart Routing System...")
            
            # Initialize tiny LLM classifier
            self.logger.log_system_event("🧠 Loading tiny LLM classifier...")
            self.tiny_classifier = TinyLLMClassifier()
            
            # Initialize main chatbot with memory
            self.logger.log_system_event("💬 Loading main chatbot...")
            from config.main_chatbot_config import LLAMA_MODEL_PATH
            self.chatbot_memory_connector = ChatbotMemoryConnector(LLAMA_MODEL_PATH)
            
            # Initialize Mistral brain (for research only)
            self.logger.log_system_event("🧠 Loading Mistral research brain...")
            try:
                self.mistral_brain = MultiTaskAssistant()
                if not self.mistral_brain.is_loaded:
                    self.logger.log_warning(ComponentType.TASK_CLASSIFIER, 
                                          "Mistral brain not loaded - research queries will use main chatbot")
                    self.mistral_brain = None
            except Exception as e:
                self.logger.log_warning(ComponentType.TASK_CLASSIFIER, 
                                      f"Mistral brain unavailable: {e}")
                self.mistral_brain = None
            
            # Initialize TTS system
            self.logger.log_system_event("🔊 Initializing TTS system...")
            self.tts_manager = TTSManager()
            self.streaming_tts_handler = StreamingTTSHandler(self.tts_manager)
            
            # Start speech thread
            self.speech_thread = threading.Thread(target=self._speech_worker, daemon=True)
            self.speech_thread.start()
            
            self.logger.log_system_event("✅ Smart Routing System ready!")
            
        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "smart routing system initialization")
            raise
    
    def _speech_worker(self):
        """Background worker for TTS processing."""
        while True:
            try:
                text = self.speech_queue.get(timeout=1.0)
                if text is None:  # Shutdown signal
                    break
                
                self.is_speaking = True
                self.tts_manager.speak(text)
                self.is_speaking = False
                self.speech_queue.task_done()
                
            except Exception as e:
                self.logger.log_error(ComponentType.TTS_SYSTEM, e, "speech worker")
                self.is_speaking = False
    
    def process_and_speak_smart(self, user_input: str) -> Dict:
        """
        Process user input with smart routing and generate spoken response.
        
        Args:
            user_input: User's query
            
        Returns:
            Dictionary with response and processing information
        """
        try:
            start_time = time.time()
            self.stats['total_queries'] += 1
            
            # Step 1: Ultra-fast classification with tiny LLM
            self.logger.log_system_event(f"🔍 Classifying query with tiny LLM...")
            classification_start = time.time()
            
            classification = self.tiny_classifier.classify_query(user_input)
            classification_time = time.time() - classification_start
            
            self.logger.log_system_event(
                f"📊 Classification: {classification.category.value} "
                f"(confidence: {classification.confidence:.2f}, "
                f"time: {classification.processing_time:.3f}s)"
            )
            
            # Step 2: Route based on classification
            response_start = time.time()
            
            if classification.category == QueryCategory.SIMPLE:
                response_data = self._handle_simple_query(user_input)
                self.stats['simple_queries'] += 1
                
            elif classification.category == QueryCategory.COMPLEX:
                response_data = self._handle_complex_query(user_input)
                self.stats['complex_queries'] += 1
                
            elif classification.category == QueryCategory.RESEARCH:
                response_data = self._handle_research_query(user_input)
                self.stats['research_queries'] += 1
                
            else:  # UNKNOWN
                response_data = self._handle_simple_query(user_input)  # Default to simple
                self.stats['simple_queries'] += 1
            
            response_time = time.time() - response_start
            total_time = time.time() - start_time
            
            # Update statistics
            self._update_stats(classification_time, total_time)
            
            # Prepare response info
            processing_info = {
                'query_type': classification.category.value,
                'classification_confidence': classification.confidence,
                'classification_time': classification_time,
                'response_time': response_time,
                'total_time': total_time,
                'processing_time': response_time,
                'routing_method': 'smart_tiny_llm'
            }
            
            return {
                'response': response_data['response'],
                'clean_response': response_data['response'],
                'processing_info': processing_info,
                'classification': classification
            }
            
        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "smart routing process")
            error_response = f"I encountered an error: {e}"
            
            return {
                'response': error_response,
                'clean_response': error_response,
                'processing_info': {
                    'query_type': 'ERROR',
                    'total_time': 0.0,
                    'classification_time': 0.0,
                    'processing_time': 0.0,
                    'error': str(e)
                },
                'classification': None
            }
    
    def _handle_simple_query(self, user_input: str) -> Dict:
        """Handle simple queries with main chatbot only."""
        self.logger.log_system_event("⚡ Processing simple query with main chatbot")
        
        # Start streaming TTS
        streaming_tts = self.streaming_tts_handler.start_streaming_response(f"simple_{int(time.time())}")
        
        try:
            # Generate response with streaming TTS
            if hasattr(self.chatbot_memory_connector.chatbot, 'generate_response_stream'):
                full_response = ""
                for chunk in self.chatbot_memory_connector.chatbot.generate_response_stream(user_input):
                    if chunk:
                        full_response += chunk
                        if streaming_tts:
                            streaming_tts.add_text(chunk)
            else:
                full_response = self.chatbot_memory_connector.chatbot.generate_response(user_input)
                if streaming_tts:
                    streaming_tts.add_text(full_response)
            
            # Finalize streaming TTS
            if streaming_tts:
                streaming_tts.finalize()
            
            # Queue for traditional TTS as backup
            self._queue_sentences_for_tts(full_response)
            
            return {'response': full_response}
            
        except Exception as e:
            if streaming_tts:
                streaming_tts.finalize()
            raise e
    
    def _handle_complex_query(self, user_input: str) -> Dict:
        """Handle complex queries with main chatbot + optional Mistral assistance."""
        self.logger.log_system_event("🔧 Processing complex query with main chatbot")
        
        # Start streaming TTS
        streaming_tts = self.streaming_tts_handler.start_streaming_response(f"complex_{int(time.time())}")
        
        try:
            # Get main response with streaming
            if hasattr(self.chatbot_memory_connector.chatbot, 'generate_response_stream'):
                main_response = ""
                for chunk in self.chatbot_memory_connector.chatbot.generate_response_stream(user_input):
                    if chunk:
                        main_response += chunk
                        if streaming_tts:
                            streaming_tts.add_text(chunk)
            else:
                main_response = self.chatbot_memory_connector.chatbot.generate_response(user_input)
                if streaming_tts:
                    streaming_tts.add_text(main_response)
            
            # Check if Mistral assistance is needed
            if self.mistral_brain and self._needs_mistral_assistance(user_input, main_response):
                self.logger.log_system_event("🧠 Requesting Mistral assistance for complex query")
                
                try:
                    # Get Mistral enhancement (but don't stream it - add to end)
                    mistral_enhancement = self.mistral_brain.enhance_response(user_input, main_response)
                    if mistral_enhancement and mistral_enhancement.strip():
                        enhanced_response = main_response + "\n\n" + mistral_enhancement
                        
                        # Add enhancement to streaming TTS
                        if streaming_tts:
                            streaming_tts.add_text("\n\n" + mistral_enhancement)
                        
                        main_response = enhanced_response
                        
                except Exception as e:
                    self.logger.log_warning(ComponentType.TASK_CLASSIFIER, 
                                          f"Mistral assistance failed: {e}")
            
            # Finalize streaming TTS
            if streaming_tts:
                streaming_tts.finalize()
            
            # Queue for traditional TTS as backup
            self._queue_sentences_for_tts(main_response)
            
            return {'response': main_response}
            
        except Exception as e:
            if streaming_tts:
                streaming_tts.finalize()
            raise e
    
    def _handle_research_query(self, user_input: str) -> Dict:
        """Handle research queries with Mistral brain (if available)."""
        if self.mistral_brain:
            self.logger.log_system_event("🔬 Processing research query with Mistral brain")
            
            try:
                # Use Mistral for deep research
                research_response = self.mistral_brain.deep_research_response(user_input)
                self._queue_sentences_for_tts(research_response)
                return {'response': research_response}
                
            except Exception as e:
                self.logger.log_warning(ComponentType.TASK_CLASSIFIER, 
                                      f"Mistral research failed: {e}")
        
        # Fallback to main chatbot for research
        self.logger.log_system_event("📚 Processing research query with main chatbot (Mistral unavailable)")
        return self._handle_complex_query(user_input)  # Treat as complex
    
    def _needs_mistral_assistance(self, query: str, response: str) -> bool:
        """Determine if a complex query needs Mistral assistance."""
        # Check for technical complexity indicators
        technical_keywords = [
            'authentication', 'security', 'database', 'architecture', 
            'system', 'implementation', 'algorithm', 'framework'
        ]
        
        urgent_indicators = ['urgent:', 'priority:', 'critical:']
        
        # Always assist urgent queries
        if any(indicator in query.lower() for indicator in urgent_indicators):
            return True
        
        # Assist technical queries
        if any(keyword in query.lower() for keyword in technical_keywords):
            return True
        
        # Assist if main response seems short for complex query
        if len(query.split()) > 10 and len(response.split()) < 50:
            return True
        
        return False
    
    def _queue_sentences_for_tts(self, text: str):
        """Queue sentences for traditional TTS processing."""
        sentences = self._extract_sentences(text)
        for sentence in sentences:
            if sentence.strip():
                self.speech_queue.put(sentence.strip())
    
    def _extract_sentences(self, text: str) -> list:
        """Extract sentences from text for TTS."""
        import re
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _update_stats(self, classification_time: float, total_time: float):
        """Update processing statistics."""
        total = self.stats['total_queries']
        
        # Update classification time average
        current_class_avg = self.stats['avg_classification_time']
        self.stats['avg_classification_time'] = ((current_class_avg * (total - 1)) + classification_time) / total
        
        # Update response time average
        current_resp_avg = self.stats['avg_response_time']
        self.stats['avg_response_time'] = ((current_resp_avg * (total - 1)) + total_time) / total
    
    def get_stats(self) -> Dict:
        """Get connector statistics."""
        return {
            'total_queries': self.stats['total_queries'],
            'simple_queries': self.stats['simple_queries'],
            'complex_queries': self.stats['complex_queries'],
            'research_queries': self.stats['research_queries'],
            'avg_classification_time': self.stats['avg_classification_time'],
            'avg_response_time': self.stats['avg_response_time'],
            'tiny_classifier_stats': self.tiny_classifier.get_stats() if self.tiny_classifier else {},
            'mistral_available': self.mistral_brain is not None,
            'speech_queue_size': self.speech_queue.qsize(),
            'currently_speaking': self.is_speaking
        }
    
    def shutdown(self):
        """Shutdown the smart routing connector."""
        self.logger.log_system_event("🔄 Shutting down Smart Routing System...")
        
        # Stop speech thread
        if self.speech_thread:
            self.speech_queue.put(None)  # Shutdown signal
            self.speech_thread.join(timeout=5)
        
        # Shutdown streaming TTS
        if self.streaming_tts_handler:
            self.streaming_tts_handler.shutdown()
        
        self.logger.log_system_event("✅ Smart Routing System shutdown complete")

# Test function
def test_smart_routing():
    """Test the smart routing connector."""
    print("🧪 Testing Smart Routing Connector")
    print("=" * 50)
    
    connector = SmartRoutingConnector()
    
    test_queries = [
        "hi there",
        "what's your name?",
        "create a secure authentication system", 
        "urgent: explain quantum computing",
        "tell me a joke"
    ]
    
    for query in test_queries:
        print(f"\nTesting: '{query}'")
        result = connector.process_and_speak_smart(query)
        
        print(f"Category: {result['processing_info']['query_type']}")
        print(f"Time: {result['processing_info']['total_time']:.3f}s")
        print(f"Response: {result['response'][:100]}...")
    
    print("\n📊 Final Statistics:")
    stats = connector.get_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    connector.shutdown()

if __name__ == "__main__":
    test_smart_routing()
