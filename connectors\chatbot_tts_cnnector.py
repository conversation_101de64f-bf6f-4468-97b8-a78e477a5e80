# main.py

from chatbot.chatbot import Cha<PERSON><PERSON>
from voice.kokoro_tts import TTSManager
import os
import threading
from queue import Queue, Empty
import sys
import io
from config.main_chatbot_config import LLAMA_MODEL_PATH

def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

class GeneratorThread(threading.Thread):
    def __init__(self, chatbot, user_input, sentence_queue):
        super().__init__()
        self.chatbot = chatbot
        self.user_input = user_input
        self.sentence_queue = sentence_queue
        self._stop_event = threading.Event()

    def run(self):
        # --- NEW, MORE ROBUST PREFIX HANDLING LOGIC ---
        
        # Buffer for the model's raw output, used only to strip the prefix
        initial_stream_buffer = ""
        # Buffer for queuing complete sentences to the TTS
        sentence_buffer = ""
        prefix_stripped = False
        
        # List of prefixes to check for and strip from the model's response
        prefixes_to_strip = sorted(["Adrina:", "Assistant:", "AI:", "A:"], key=len, reverse=True)
        # We check for a prefix up to the length of the longest possible one
        max_prefix_len = len(prefixes_to_strip[0]) if prefixes_to_strip else 0

        # Capture Llama debug output
        original_stdout = sys.stdout
        original_stderr = sys.stderr
        sys.stderr = io.StringIO()

        try:
            for chunk in self.chatbot.generate_response_stream(self.user_input):
                if self._stop_event.is_set():
                    break
                
                output_this_iteration = chunk

                if not prefix_stripped:
                    initial_stream_buffer += chunk
                    # Check against a whitespace-trimmed version of the buffer
                    temp_check_buffer = initial_stream_buffer.lstrip()

                    # Check if the buffer now starts with a known prefix
                    for prefix in prefixes_to_strip:
                        if temp_check_buffer.lower().startswith(prefix.lower()):
                            # Prefix found. The actual content starts *after* it.
                            prefix_end_index = initial_stream_buffer.lower().find(prefix.lower()) + len(prefix)
                            output_this_iteration = initial_stream_buffer[prefix_end_index:]
                            prefix_stripped = True
                            break # Exit prefix-checking loop
                    
                    if not prefix_stripped:
                        # If we've buffered more than the max prefix length and found nothing,
                        # assume no prefix is coming. Release the buffer.
                        if len(temp_check_buffer) > max_prefix_len:
                            output_this_iteration = initial_stream_buffer
                            prefix_stripped = True
                        else:
                            # Still buffering and checking for a prefix. Don't output anything yet.
                            output_this_iteration = ""

                # Now, output_this_iteration contains the correct text to display and queue
                if output_this_iteration:
                    original_stdout.write(output_this_iteration)
                    original_stdout.flush()
                    
                    sentence_buffer += output_this_iteration
                    
                    # Process complete sentences for the TTS queue
                    while any(p in sentence_buffer for p in ".!?"):
                        pos = min(sentence_buffer.find(p) for p in ".!?" if p in sentence_buffer) + 1
                        sentence = sentence_buffer[:pos].strip()
                        sentence_buffer = sentence_buffer[pos:]
                        if sentence:
                            self.sentence_queue.put(sentence)

            # After the loop, if we were still buffering, flush the remaining content
            if not prefix_stripped and initial_stream_buffer:
                original_stdout.write(initial_stream_buffer)
                original_stdout.flush()
                sentence_buffer += initial_stream_buffer

            # Queue any remaining text in the buffer
            if sentence_buffer.strip():
                self.sentence_queue.put(sentence_buffer.strip())
                
            # Signal the end of generation to the speech thread
            self.sentence_queue.put(None)

        finally:
            # Restore standard streams
            sys.stderr = original_stderr

    def stop(self):
        self._stop_event.set()

class SpeechThread(threading.Thread):
    def __init__(self, sentence_queue, tts_manager):
        super().__init__()
        self.sentence_queue = sentence_queue
        self.tts_manager = tts_manager
        self._stop_event = threading.Event()

    def run(self):
        while not self._stop_event.is_set():
            try:
                sentence = self.sentence_queue.get(timeout=0.1)
                if sentence is None:  # End signal
                    self.sentence_queue.task_done()
                    break
                
                self.tts_manager.speak(sentence)
                self.sentence_queue.task_done()
            except Empty:
                continue

    def stop(self):
        self._stop_event.set()

def main():
    if not os.path.exists(LLAMA_MODEL_PATH):
        print(f"Error: Model file '{LLAMA_MODEL_PATH}' not found!")
        return

    print("Initializing chatbot...")
    chatbot = Chatbot(LLAMA_MODEL_PATH)
    
    print("Initializing TTS...")
    tts_manager = TTSManager()

    clear_screen()
    
    print("=" * 50)
    print("Welcome to the Speaking Chatbot!")
    print("Type 'quit' to exit")
    print("Type 'clear' to clear the screen")
    print("=" * 50 + "\n")

    try:
        while True:
            user_input = input("You: ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'clear':
                clear_screen()
                continue
            elif not user_input:
                continue
            
            print("\nAdrina: ", end="", flush=True)
            
            sentence_queue = Queue()
            
            generator = GeneratorThread(chatbot, user_input, sentence_queue)
            speech = SpeechThread(sentence_queue, tts_manager)
            
            try:
                generator.start()
                speech.start()
                
                generator.join()
                sentence_queue.join()
                
                speech.stop()
                if speech.is_alive():
                    speech.join()

                print("\n")
                
            except KeyboardInterrupt:
                print("\nStopping...")
                generator.stop()
                speech.stop()
                if generator.is_alive():
                    generator.join()
                if speech.is_alive():
                    speech.join()
                continue
                
    except (KeyboardInterrupt, EOFError):
        print("\nGoodbye!")
    finally:
        print("Shutting down TTS...")
        tts_manager.stop()
