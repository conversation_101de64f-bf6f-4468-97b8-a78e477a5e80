#!/usr/bin/env python3
"""
Smart Memory Connector for Adrina AI Assistant v2.0

Intelligent memory connector that automatically chooses the best embedding service:
- Local nomic embeddings (preferred, 100% offline)
- Ollama embeddings (fallback, requires server)

Drop-in replacement for the original MemoryConnector.
"""

import sys
import os
import logging
from typing import Optional, Dict, Any, List

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from memory.enhanced_memory_manager import EnhancedMemoryManager

class SmartMemoryConnector:
    """
    Smart memory connector with automatic embedding service selection.
    
    Provides the same interface as MemoryConnector but with intelligent
    service selection between local and Ollama embeddings.
    """
    
    def __init__(self,
                 memory_db_path: str = "./memory/memory_db",
                 enable_memory: bool = True,
                 context_window: int = 3,
                 prefer_local: bool = True,
                 model_path: str = "models/nomic-embed-text"):
        """
        Initialize the smart memory connector.
        
        Args:
            memory_db_path: Path for memory database storage
            enable_memory: Whether to enable memory functionality
            context_window: Number of relevant memories to include in context
            prefer_local: Whether to prefer local embeddings over Ollama
            model_path: Path to local nomic model
        """
        self.logger = logging.getLogger(__name__)
        self.enable_memory = enable_memory
        self.context_window = context_window
        self.memory_manager = None
        
        # Initialize memory system
        if self.enable_memory:
            try:
                self.memory_manager = EnhancedMemoryManager(
                    persist_directory=memory_db_path,
                    prefer_local=prefer_local,
                    model_path=model_path
                )
                
                if self.memory_manager.is_available():
                    self.logger.info(f"✅ Smart memory system initialized with {self.memory_manager.service_type} embeddings")
                else:
                    self.logger.error("❌ No embedding service available")
                    self.enable_memory = False
                    
            except Exception as e:
                self.logger.error(f"❌ Failed to initialize memory system: {e}")
                self.logger.info("Continuing without memory...")
                self.enable_memory = False
        
        # Session tracking
        self.current_session_id = None
        self.conversation_count = 0
    
    def start_session(self, session_id: Optional[str] = None) -> str:
        """Start a new conversation session."""
        import uuid
        self.current_session_id = session_id or str(uuid.uuid4())[:8]
        self.conversation_count = 0
        self.logger.info(f"Started memory session: {self.current_session_id}")
        return self.current_session_id
    
    def get_conversation_context(self, user_input: str) -> str:
        """
        Get relevant memory context for the user input.
        
        Args:
            user_input: Current user input
            
        Returns:
            Formatted context string
        """
        if not self.enable_memory or not self.memory_manager:
            return ""
        
        try:
            # Get conversation context
            context = self.memory_manager.get_conversation_context(
                user_input, 
                max_context_items=self.context_window
            )
            
            # Get relevant facts and preferences
            relevant_memories = self.memory_manager.retrieve_relevant_memories(
                user_input, 
                memory_types=["fact", "preference"],
                max_results=2
            )
            
            # Format additional context with direct fact extraction
            if relevant_memories:
                additional_context = []
                direct_facts = []

                for memory in relevant_memories:
                    memory_type = memory["metadata"].get("memory_type", "unknown")
                    text = memory["text"]
                    text_lower = text.lower()
                    user_lower = user_input.lower()

                    # Extract specific facts for direct questions
                    if "pet" in user_lower and "name" in user_lower:
                        if "bruno" in text_lower:
                            direct_facts.append("FACT: The user's pet name is Bruno.")

                    if ("my name" in user_lower or "user name" in user_lower) and "what" in user_lower:
                        if "anirban" in text_lower and "das" in text_lower:
                            direct_facts.append("FACT: The user's name is Anirban Das.")
                        elif "anirban" in text_lower:
                            direct_facts.append("FACT: The user's name is Anirban.")

                    additional_context.append(f"[{memory_type.title()}] {text}")

                # Prioritize direct facts
                if direct_facts:
                    fact_context = "STORED FACTS - USE THESE EXACT ANSWERS:\n" + "\n".join(direct_facts)
                    if context:
                        context = fact_context + "\n\n" + context
                    else:
                        context = fact_context

                    if additional_context:
                        context += "\n\nAdditional Context:\n" + "\n".join(additional_context)
                elif additional_context:
                    if context:
                        context += "\n\nRelevant Information:\n" + "\n".join(additional_context)
                    else:
                        context = "Relevant Information:\n" + "\n".join(additional_context)
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error getting memory context: {e}")
            return ""
    
    def store_conversation(self, user_input: str, ai_response: str) -> bool:
        """
        Store a conversation exchange in memory.
        
        Args:
            user_input: User's input text
            ai_response: AI's response text
            
        Returns:
            True if stored successfully, False otherwise
        """
        if not self.enable_memory or not self.memory_manager:
            return False
        
        try:
            metadata = {
                "session_id": self.current_session_id,
                "conversation_number": self.conversation_count
            }
            
            memory_ids = self.memory_manager.store_conversation(
                user_input, 
                ai_response, 
                metadata
            )
            
            if memory_ids:
                self.conversation_count += 1
                self.logger.debug(f"Stored conversation: {len(memory_ids)} memories")
                return True
            else:
                self.logger.warning("Failed to store conversation")
                return False
                
        except Exception as e:
            self.logger.error(f"Error storing conversation: {e}")
            return False
    
    def store_fact(self, fact: str, category: str = "general", importance: int = 5) -> bool:
        """Store a fact in memory."""
        if not self.enable_memory or not self.memory_manager:
            return False
        
        try:
            fact_id = self.memory_manager.store_fact(fact, category, importance)
            if fact_id:
                self.logger.debug(f"Stored fact: {fact_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error storing fact: {e}")
            return False
    
    def store_preference(self, preference: str, user_id: str = "default") -> bool:
        """Store a user preference in memory."""
        if not self.enable_memory or not self.memory_manager:
            return False
        
        try:
            pref_id = self.memory_manager.store_preference(preference, user_id)
            if pref_id:
                self.logger.debug(f"Stored preference: {pref_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error storing preference: {e}")
            return False
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics."""
        if not self.enable_memory or not self.memory_manager:
            return {
                "memory_enabled": False,
                "session_id": self.current_session_id,
                "conversation_count": self.conversation_count
            }
        
        try:
            stats = self.memory_manager.get_memory_summary()
            stats.update({
                "memory_enabled": True,
                "session_id": self.current_session_id,
                "conversation_count": self.conversation_count
            })
            return stats
        except Exception as e:
            self.logger.error(f"Error getting memory stats: {e}")
            return {"error": str(e)}
    
    def search_memories(self, query: str, memory_types: Optional[List[str]] = None, max_results: int = 5) -> List[Dict[str, Any]]:
        """Search for memories matching a query."""
        if not self.enable_memory or not self.memory_manager:
            return []
        
        try:
            return self.memory_manager.retrieve_relevant_memories(
                query, 
                memory_types=memory_types,
                max_results=max_results
            )
        except Exception as e:
            self.logger.error(f"Error searching memories: {e}")
            return []
    
    def is_memory_enabled(self) -> bool:
        """Check if memory system is enabled and working."""
        return self.enable_memory and self.memory_manager is not None and self.memory_manager.is_available()
    
    def disable_memory(self):
        """Temporarily disable memory functionality."""
        self.enable_memory = False
        self.logger.info("Memory functionality disabled")
    
    def enable_memory_system(self):
        """Re-enable memory functionality."""
        if self.memory_manager is not None and self.memory_manager.is_available():
            self.enable_memory = True
            self.logger.info("Memory functionality enabled")
        else:
            self.logger.warning("Cannot enable memory - memory system not available")
    
    def clear_session(self):
        """Clear the current session."""
        self.current_session_id = None
        self.conversation_count = 0
        self.logger.info("Memory session cleared")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the memory connector."""
        status = {
            "memory_enabled": self.enable_memory,
            "memory_manager_available": self.memory_manager is not None,
            "current_session_id": self.current_session_id,
            "conversation_count": self.conversation_count,
            "context_window": self.context_window
        }
        
        if self.memory_manager:
            status.update({
                "embedding_service": self.memory_manager.service_type,
                "system_available": self.memory_manager.is_available()
            })
        
        return status


# Example usage and testing
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Test the smart memory connector
    print("🧪 Testing Smart Memory Connector")
    print("=" * 40)
    
    try:
        # Initialize smart memory connector
        memory_connector = SmartMemoryConnector()
        
        # Check status
        status = memory_connector.get_status()
        print(f"Memory Status: {status}")
        
        if memory_connector.is_memory_enabled():
            print(f"✅ Using {status.get('embedding_service', 'Unknown')} embeddings")
            
            # Start a session
            session_id = memory_connector.start_session()
            print(f"Started session: {session_id}")
            
            # Test storing a conversation
            user_input = "Hello, I want to learn about Python programming"
            ai_response = "I'd be happy to help you learn Python! Let's start with the basics."
            
            success = memory_connector.store_conversation(user_input, ai_response)
            print(f"Stored conversation: {'✅' if success else '❌'}")
            
            # Test getting context
            context = memory_connector.get_conversation_context("Tell me more about Python")
            print(f"Context retrieved: {'✅' if context else '❌'}")
            if context:
                print(f"Context preview: {context[:100]}...")
            
            # Test storing a fact
            fact_success = memory_connector.store_fact("Python is a high-level programming language", "programming")
            print(f"Stored fact: {'✅' if fact_success else '❌'}")
            
            # Get memory stats
            stats = memory_connector.get_memory_stats()
            print(f"Memory stats: {stats}")
            
        else:
            print("❌ Memory system is not available")
            print("Make sure you have either:")
            print("  1. Local nomic model downloaded (preferred)")
            print("  2. Ollama running with nomic-embed-text model")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
    
    print("\n🎉 Smart memory connector test completed!")
