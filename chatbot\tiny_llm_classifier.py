#!/usr/bin/env python3
"""
Tiny LLM Classifier for Adrina AI Assistant v2.0

Ultra-fast query classification using a lightweight LLM model.
Replaces heavy Mistral classification for speed optimization.
"""

import os
import sys
import time
from typing import Tuple, Optional
from dataclasses import dataclass
from enum import Enum

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

try:
    from llama_cpp import Llama
except ImportError:
    print("Warning: llama-cpp-python not installed. Tiny LLM classifier will not work.")
    Llama = None

from universal_logging.universal_logger import ComponentType, get_logger

class QueryCategory(Enum):
    """Query classification categories."""
    SIMPLE = "SIMPLE"      # Basic greetings, simple questions
    COMPLEX = "COMPLEX"    # Technical tasks, creation requests
    RESEARCH = "RESEARCH"  # Deep analysis, research queries
    UNKNOWN = "UNKNOWN"    # Fallback category

@dataclass
class ClassificationResult:
    """Result of query classification."""
    category: QueryCategory
    confidence: float
    processing_time: float
    reasoning: str = ""

class TinyLLMClassifier:
    """
    Ultra-fast query classifier using a tiny LLM model.
    
    Designed to replace heavy Mistral classification with sub-second
    classification times while maintaining accuracy.
    """
    
    def __init__(self, model_path: Optional[str] = None):
        self.logger = get_logger()
        self.model = None
        self.model_path = model_path or self._get_default_model_path()
        self.is_loaded = False
        
        # Classification statistics
        self.stats = {
            'total_classifications': 0,
            'avg_processing_time': 0.0,
            'category_counts': {cat.value: 0 for cat in QueryCategory}
        }
        
        # Load model
        self._load_model()
    
    def _get_default_model_path(self) -> str:
        """Get default tiny LLM model path."""
        # Try common tiny model locations
        possible_paths = [
            "models/phi-3-mini.gguf",
            "models/qwen2-1.5b.gguf", 
            "models/tinyllama-1.1b.gguf",
            "models/gemma-2b.gguf"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # Fallback - will need to be downloaded
        return "models/phi-3-mini.gguf"
    
    def _load_model(self):
        """Load the tiny LLM model."""
        if not Llama:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, 
                                "llama-cpp-python not available", 
                                "tiny LLM classifier initialization")
            return
        
        if not os.path.exists(self.model_path):
            self.logger.log_error(ComponentType.TASK_CLASSIFIER,
                                f"Model file not found: {self.model_path}",
                                "tiny LLM classifier initialization")
            return
        
        try:
            self.logger.log_system_event(f"🧠 Loading tiny LLM: {self.model_path}")
            start_time = time.time()
            
            self.model = Llama(
                model_path=self.model_path,
                n_ctx=512,  # Small context for speed
                n_threads=4,
                n_gpu_layers=-1,  # Use GPU if available
                verbose=False
            )
            
            load_time = time.time() - start_time
            self.is_loaded = True
            
            self.logger.log_system_event(f"✅ Tiny LLM loaded in {load_time:.2f}s")
            
        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "loading tiny LLM model")
            self.is_loaded = False
    
    def classify_query(self, query: str) -> ClassificationResult:
        """
        Classify a user query into categories.
        
        Args:
            query: User input to classify
            
        Returns:
            ClassificationResult with category, confidence, and timing
        """
        start_time = time.time()
        
        # Fallback to pattern-based classification if model not loaded
        if not self.is_loaded or not self.model:
            return self._pattern_based_classification(query, start_time)
        
        try:
            # Create classification prompt
            prompt = self._create_classification_prompt(query)
            
            # Generate classification
            response = self.model(
                prompt,
                max_tokens=20,
                temperature=0.1,
                stop=["</classification>", "\n\n"],
                echo=False
            )
            
            # Parse response
            result = self._parse_classification_response(response['choices'][0]['text'])
            
            # Calculate timing
            processing_time = time.time() - start_time
            
            # Update statistics
            self._update_stats(result.category, processing_time)
            
            return ClassificationResult(
                category=result.category,
                confidence=result.confidence,
                processing_time=processing_time,
                reasoning=result.reasoning
            )
            
        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "tiny LLM classification")
            return self._pattern_based_classification(query, start_time)
    
    def _create_classification_prompt(self, query: str) -> str:
        """Create classification prompt for the tiny LLM."""
        return f"""<classification>
Query: "{query}"

Classify into one category:

SIMPLE: Basic greetings (hi, hello, bye), simple questions (name, joke, weather), casual chat
COMPLEX: Technical tasks (create, build, develop), urgent requests, multi-step instructions  
RESEARCH: Deep analysis, detailed explanations, research topics, complex concepts

Format: CATEGORY|confidence(0.0-1.0)
Example: SIMPLE|0.9

Classification: """
    
    def _parse_classification_response(self, response: str) -> ClassificationResult:
        """Parse the LLM classification response."""
        try:
            # Clean response
            response = response.strip().upper()
            
            # Try to parse CATEGORY|confidence format
            if '|' in response:
                parts = response.split('|')
                category_str = parts[0].strip()
                confidence = float(parts[1].strip())
            else:
                # Fallback - just category
                category_str = response.strip()
                confidence = 0.7
            
            # Map to enum
            try:
                category = QueryCategory(category_str)
            except ValueError:
                category = QueryCategory.UNKNOWN
                confidence = 0.5
            
            return ClassificationResult(
                category=category,
                confidence=confidence,
                processing_time=0.0,  # Will be set by caller
                reasoning=f"LLM classified as {category_str}"
            )
            
        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "parsing classification response")
            return ClassificationResult(
                category=QueryCategory.UNKNOWN,
                confidence=0.3,
                processing_time=0.0,
                reasoning="Parse error - using fallback"
            )
    
    def _pattern_based_classification(self, query: str, start_time: float) -> ClassificationResult:
        """Fallback pattern-based classification when LLM is unavailable."""
        query_lower = query.lower().strip()
        
        # Simple patterns
        simple_patterns = [
            'hi', 'hello', 'hey', 'goodbye', 'bye', 'thanks', 'thank you',
            'what is your name', 'who are you', 'how are you',
            'tell me a joke', 'joke', 'story', 'weather'
        ]
        
        # Complex patterns
        complex_keywords = [
            'create', 'build', 'develop', 'implement', 'design', 'make',
            'urgent:', 'priority:', 'help me', 'how to', 'step by step',
            'authentication', 'system', 'database', 'code', 'program'
        ]
        
        # Research patterns  
        research_keywords = [
            'explain', 'what is', 'how does', 'why', 'research', 'analyze',
            'detailed', 'comprehensive', 'in depth', 'theory', 'concept'
        ]
        
        processing_time = time.time() - start_time
        
        # Classification logic
        if any(pattern in query_lower for pattern in simple_patterns):
            category = QueryCategory.SIMPLE
            confidence = 0.8
            reasoning = "Pattern match: simple greeting/question"
        elif any(keyword in query_lower for keyword in complex_keywords):
            category = QueryCategory.COMPLEX  
            confidence = 0.7
            reasoning = "Pattern match: complex task keywords"
        elif any(keyword in query_lower for keyword in research_keywords):
            category = QueryCategory.RESEARCH
            confidence = 0.7
            reasoning = "Pattern match: research keywords"
        elif len(query.split()) > 15:
            category = QueryCategory.RESEARCH
            confidence = 0.6
            reasoning = "Long query - likely research"
        else:
            category = QueryCategory.SIMPLE
            confidence = 0.5
            reasoning = "Default: treating as simple"
        
        self._update_stats(category, processing_time)
        
        return ClassificationResult(
            category=category,
            confidence=confidence,
            processing_time=processing_time,
            reasoning=reasoning
        )
    
    def _update_stats(self, category: QueryCategory, processing_time: float):
        """Update classification statistics."""
        self.stats['total_classifications'] += 1
        self.stats['category_counts'][category.value] += 1
        
        # Update average processing time
        total = self.stats['total_classifications']
        current_avg = self.stats['avg_processing_time']
        self.stats['avg_processing_time'] = ((current_avg * (total - 1)) + processing_time) / total
    
    def get_stats(self) -> dict:
        """Get classification statistics."""
        return {
            'model_loaded': self.is_loaded,
            'model_path': self.model_path,
            'total_classifications': self.stats['total_classifications'],
            'avg_processing_time': self.stats['avg_processing_time'],
            'category_distribution': self.stats['category_counts']
        }

# Test function
def test_tiny_classifier():
    """Test the tiny LLM classifier."""
    print("🧪 Testing Tiny LLM Classifier")
    print("=" * 50)
    
    classifier = TinyLLMClassifier()
    
    test_queries = [
        "hi there",
        "what's your name?", 
        "tell me a joke",
        "create a secure authentication system",
        "urgent: fix the database connection",
        "explain quantum computing in detail",
        "how does machine learning work?",
        "goodbye"
    ]
    
    for query in test_queries:
        result = classifier.classify_query(query)
        print(f"Query: '{query}'")
        print(f"  Category: {result.category.value}")
        print(f"  Confidence: {result.confidence:.2f}")
        print(f"  Time: {result.processing_time:.3f}s")
        print(f"  Reasoning: {result.reasoning}")
        print()
    
    print("📊 Final Statistics:")
    stats = classifier.get_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_tiny_classifier()
