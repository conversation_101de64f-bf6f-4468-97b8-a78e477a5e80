#!/usr/bin/env python3
"""
Error Alert System for Adrina AI Assistant v2.0

Real-time error alerting system that:
- Maintains a high-priority error queue
- Provides detailed error descriptions and solutions
- Alerts users immediately about critical issues
- Cleans up temporary error storage after user notification
"""

import time
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
from queue import Queue, Empty
from datetime import datetime, timedelta

class ErrorSeverity(Enum):
    """Error severity levels."""
    CRITICAL = "critical"      # System-breaking errors
    HIGH = "high"             # Major functionality issues
    MEDIUM = "medium"         # Minor functionality issues
    LOW = "low"              # Warnings and info

class ErrorCategory(Enum):
    """Error categories for better classification."""
    SYSTEM_STARTUP = "system_startup"
    MODEL_LOADING = "model_loading"
    MEMORY_SYSTEM = "memory_system"
    TTS_SYSTEM = "tts_system"
    NETWORK_CONNECTION = "network_connection"
    FILE_SYSTEM = "file_system"
    CONFIGURATION = "configuration"
    INTEGRATION = "integration"
    PERFORMANCE = "performance"
    USER_INPUT = "user_input"

@dataclass
class ErrorAlert:
    """Detailed error alert with context and solutions."""
    error_id: str
    timestamp: datetime
    severity: ErrorSeverity
    category: ErrorCategory
    component: str
    error_type: str
    error_message: str
    context: str
    user_friendly_description: str
    suggested_solutions: List[str]
    technical_details: Dict[str, Any]
    query_id: Optional[str] = None
    resolved: bool = False
    user_notified: bool = False

class ErrorAlertSystem:
    """
    Manages real-time error alerts and user notifications.
    """
    
    def __init__(self, max_queue_size: int = 100, cleanup_interval: int = 300):
        self.max_queue_size = max_queue_size
        self.cleanup_interval = cleanup_interval  # 5 minutes
        
        # Error storage
        self.error_queue = Queue(maxsize=max_queue_size)
        self.error_history = []  # Resolved errors
        self.active_errors = {}  # error_id -> ErrorAlert
        
        # Threading
        self.cleanup_thread = None
        self.shutdown_event = threading.Event()
        self._lock = threading.Lock()
        
        # Statistics
        self.stats = {
            'total_errors': 0,
            'critical_errors': 0,
            'high_priority_errors': 0,
            'resolved_errors': 0,
            'user_notifications': 0
        }
        
        # Error pattern recognition
        self.error_patterns = self._initialize_error_patterns()
        
        # Start cleanup thread
        self._start_cleanup_thread()
    
    def _initialize_error_patterns(self) -> Dict[str, Dict]:
        """Initialize error patterns for better classification and solutions."""
        return {
            # Model Loading Errors
            "model_not_found": {
                "pattern": ["no such file", "file not found", "model path"],
                "category": ErrorCategory.MODEL_LOADING,
                "severity": ErrorSeverity.CRITICAL,
                "description": "AI model file is missing or path is incorrect",
                "solutions": [
                    "Check if the model file exists at the specified path",
                    "Verify the model path in configuration files",
                    "Re-download the model if it's corrupted",
                    "Check file permissions for the model directory"
                ]
            },
            
            # Memory System Errors
            "memory_connection_failed": {
                "pattern": ["connection refused", "chromadb", "embedding"],
                "category": ErrorCategory.MEMORY_SYSTEM,
                "severity": ErrorSeverity.HIGH,
                "description": "Memory system (ChromaDB) connection failed",
                "solutions": [
                    "Check if ChromaDB service is running",
                    "Verify database path and permissions",
                    "Restart the memory system",
                    "Check for port conflicts"
                ]
            },
            
            # TTS System Errors
            "tts_initialization_failed": {
                "pattern": ["tts", "audio", "kokoro", "speak"],
                "category": ErrorCategory.TTS_SYSTEM,
                "severity": ErrorSeverity.MEDIUM,
                "description": "Text-to-Speech system failed to initialize",
                "solutions": [
                    "Check audio device availability",
                    "Verify TTS model files are present",
                    "Check audio drivers and permissions",
                    "Try restarting the audio service"
                ]
            },
            
            # Integration Errors
            "method_not_found": {
                "pattern": ["has no attribute", "method", "object"],
                "category": ErrorCategory.INTEGRATION,
                "severity": ErrorSeverity.HIGH,
                "description": "Component integration error - method or attribute missing",
                "solutions": [
                    "Check component compatibility versions",
                    "Verify method names in integration code",
                    "Update component interfaces",
                    "Check for recent code changes"
                ]
            },
            
            # Configuration Errors
            "config_missing": {
                "pattern": ["config", "configuration", "setting", "parameter"],
                "category": ErrorCategory.CONFIGURATION,
                "severity": ErrorSeverity.MEDIUM,
                "description": "Configuration parameter missing or invalid",
                "solutions": [
                    "Check configuration file syntax",
                    "Verify all required parameters are set",
                    "Reset to default configuration",
                    "Check configuration file permissions"
                ]
            },
            
            # Performance Errors
            "timeout_error": {
                "pattern": ["timeout", "timed out", "connection timeout"],
                "category": ErrorCategory.PERFORMANCE,
                "severity": ErrorSeverity.MEDIUM,
                "description": "Operation timed out - performance issue detected",
                "solutions": [
                    "Check system resources (CPU, Memory)",
                    "Increase timeout values if appropriate",
                    "Check network connectivity",
                    "Monitor system performance"
                ]
            },
            
            # File System Errors
            "file_permission": {
                "pattern": ["permission denied", "access denied", "forbidden"],
                "category": ErrorCategory.FILE_SYSTEM,
                "severity": ErrorSeverity.HIGH,
                "description": "File system permission error",
                "solutions": [
                    "Check file and directory permissions",
                    "Run with appropriate user privileges",
                    "Verify file ownership",
                    "Check disk space availability"
                ]
            }
        }
    
    def _start_cleanup_thread(self):
        """Start the cleanup thread for resolved errors."""
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_worker,
            daemon=True,
            name="ErrorCleanup"
        )
        self.cleanup_thread.start()
    
    def _cleanup_worker(self):
        """Worker thread for cleaning up resolved errors."""
        while not self.shutdown_event.is_set():
            try:
                self._cleanup_resolved_errors()
                time.sleep(self.cleanup_interval)
            except Exception as e:
                print(f"Error in cleanup worker: {e}")
    
    def _cleanup_resolved_errors(self):
        """Clean up resolved errors that have been user-notified."""
        with self._lock:
            current_time = datetime.now()
            cleanup_threshold = current_time - timedelta(minutes=10)  # Keep for 10 minutes after resolution
            
            # Move old resolved errors to history
            errors_to_remove = []
            for error_id, error_alert in self.active_errors.items():
                if (error_alert.resolved and error_alert.user_notified and 
                    error_alert.timestamp < cleanup_threshold):
                    errors_to_remove.append(error_id)
                    self.error_history.append(error_alert)
            
            for error_id in errors_to_remove:
                del self.active_errors[error_id]
            
            # Limit history size
            if len(self.error_history) > 1000:
                self.error_history = self.error_history[-500:]  # Keep last 500
    
    def add_error(self, component: str, error: Exception, context: str = "", 
                  query_id: Optional[str] = None) -> str:
        """
        Add an error to the alert system.
        
        Returns:
            error_id for tracking
        """
        error_id = f"err_{int(time.time() * 1000) % 100000:05d}"
        
        # Analyze error for classification
        error_analysis = self._analyze_error(error, context, component)
        
        # Create error alert
        error_alert = ErrorAlert(
            error_id=error_id,
            timestamp=datetime.now(),
            severity=error_analysis['severity'],
            category=error_analysis['category'],
            component=component,
            error_type=type(error).__name__,
            error_message=str(error),
            context=context,
            user_friendly_description=error_analysis['description'],
            suggested_solutions=error_analysis['solutions'],
            technical_details={
                'exception_type': type(error).__name__,
                'exception_args': error.args,
                'context': context,
                'component': component
            },
            query_id=query_id
        )
        
        # Add to active errors and queue
        with self._lock:
            self.active_errors[error_id] = error_alert
            
            # Add to queue if high priority
            if error_alert.severity in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH]:
                try:
                    self.error_queue.put_nowait(error_alert)
                except:
                    pass  # Queue full, skip
            
            # Update statistics
            self.stats['total_errors'] += 1
            if error_alert.severity == ErrorSeverity.CRITICAL:
                self.stats['critical_errors'] += 1
            elif error_alert.severity == ErrorSeverity.HIGH:
                self.stats['high_priority_errors'] += 1
        
        return error_id
    
    def _analyze_error(self, error: Exception, context: str, component: str) -> Dict:
        """Analyze error to determine category, severity, and solutions."""
        error_text = f"{str(error)} {context} {component}".lower()
        
        # Check against known patterns
        for pattern_name, pattern_info in self.error_patterns.items():
            if any(pattern in error_text for pattern in pattern_info['pattern']):
                return {
                    'category': pattern_info['category'],
                    'severity': pattern_info['severity'],
                    'description': pattern_info['description'],
                    'solutions': pattern_info['solutions']
                }
        
        # Default classification
        return {
            'category': ErrorCategory.SYSTEM_STARTUP,
            'severity': ErrorSeverity.MEDIUM,
            'description': f"An error occurred in {component}: {str(error)}",
            'solutions': [
                "Check the system logs for more details",
                "Try restarting the affected component",
                "Verify system configuration",
                "Contact support if the issue persists"
            ]
        }
    
    def get_pending_alerts(self, max_alerts: int = 5) -> List[ErrorAlert]:
        """Get pending high-priority alerts for user notification."""
        alerts = []
        
        try:
            while len(alerts) < max_alerts:
                try:
                    alert = self.error_queue.get_nowait()
                    if not alert.user_notified:
                        alerts.append(alert)
                except Empty:
                    break
        except Exception:
            pass
        
        return alerts
    
    def mark_user_notified(self, error_id: str):
        """Mark an error as user-notified."""
        with self._lock:
            if error_id in self.active_errors:
                self.active_errors[error_id].user_notified = True
                self.stats['user_notifications'] += 1
    
    def resolve_error(self, error_id: str):
        """Mark an error as resolved."""
        with self._lock:
            if error_id in self.active_errors:
                self.active_errors[error_id].resolved = True
                self.stats['resolved_errors'] += 1
    
    def get_active_errors(self) -> List[ErrorAlert]:
        """Get all active (unresolved) errors."""
        with self._lock:
            return [alert for alert in self.active_errors.values() if not alert.resolved]
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get error summary for display."""
        with self._lock:
            active_errors = len([e for e in self.active_errors.values() if not e.resolved])
            pending_notifications = self.error_queue.qsize()
            
            return {
                'active_errors': active_errors,
                'pending_notifications': pending_notifications,
                'statistics': dict(self.stats),
                'categories': self._get_error_categories(),
                'recent_errors': self._get_recent_errors(5)
            }
    
    def _get_error_categories(self) -> Dict[str, int]:
        """Get error count by category."""
        categories = {}
        for error_alert in self.active_errors.values():
            if not error_alert.resolved:
                cat = error_alert.category.value
                categories[cat] = categories.get(cat, 0) + 1
        return categories
    
    def _get_recent_errors(self, count: int) -> List[Dict]:
        """Get recent errors for summary."""
        recent = sorted(
            [e for e in self.active_errors.values() if not e.resolved],
            key=lambda x: x.timestamp,
            reverse=True
        )[:count]
        
        return [
            {
                'error_id': e.error_id,
                'severity': e.severity.value,
                'category': e.category.value,
                'description': e.user_friendly_description,
                'timestamp': e.timestamp.strftime('%H:%M:%S')
            }
            for e in recent
        ]
    
    def shutdown(self):
        """Shutdown the error alert system."""
        self.shutdown_event.set()
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)

# Global error alert system instance
_error_alert_system = None

def get_error_alert_system() -> ErrorAlertSystem:
    """Get the global error alert system instance."""
    global _error_alert_system
    if _error_alert_system is None:
        _error_alert_system = ErrorAlertSystem()
    return _error_alert_system

def add_error_alert(component: str, error: Exception, context: str = "", 
                   query_id: Optional[str] = None) -> str:
    """Convenience function to add error alert."""
    return get_error_alert_system().add_error(component, error, context, query_id)

def get_pending_error_alerts(max_alerts: int = 5) -> List[ErrorAlert]:
    """Convenience function to get pending alerts."""
    return get_error_alert_system().get_pending_alerts(max_alerts)

def mark_error_notified(error_id: str):
    """Convenience function to mark error as notified."""
    get_error_alert_system().mark_user_notified(error_id)

def get_error_summary() -> Dict[str, Any]:
    """Convenience function to get error summary."""
    return get_error_alert_system().get_error_summary()

# Test function
def test_error_alert_system():
    """Test the error alert system."""
    print("🧪 Testing Error Alert System")
    print("=" * 50)
    
    system = ErrorAlertSystem()
    
    # Test different types of errors
    test_errors = [
        (ValueError("Model file not found at path"), "model loading", "CHATBOT"),
        (ConnectionError("ChromaDB connection refused"), "memory initialization", "MEMORY"),
        (AttributeError("'TTSManager' object has no attribute 'speak_text'"), "TTS integration", "TTS"),
        (PermissionError("Access denied to log file"), "file system", "LOGGER")
    ]
    
    print("\n🚨 Adding test errors...")
    error_ids = []
    for error, context, component in test_errors:
        error_id = system.add_error(component, error, context)
        error_ids.append(error_id)
        print(f"Added error {error_id}: {error}")
    
    print(f"\n📊 Error Summary:")
    summary = system.get_error_summary()
    print(f"Active errors: {summary['active_errors']}")
    print(f"Pending notifications: {summary['pending_notifications']}")
    print(f"Categories: {summary['categories']}")
    
    print(f"\n🔔 Pending alerts:")
    alerts = system.get_pending_alerts()
    for alert in alerts:
        print(f"- {alert.error_id}: {alert.user_friendly_description}")
        print(f"  Severity: {alert.severity.value} | Category: {alert.category.value}")
        print(f"  Solutions: {alert.suggested_solutions[0]}")
    
    # Mark as notified and resolved
    print(f"\n✅ Marking errors as notified and resolved...")
    for error_id in error_ids:
        system.mark_user_notified(error_id)
        system.resolve_error(error_id)
    
    print(f"Final stats: {system.get_error_summary()['statistics']}")
    
    system.shutdown()
    print("\n✅ Error alert system test completed!")

if __name__ == "__main__":
    test_error_alert_system()
