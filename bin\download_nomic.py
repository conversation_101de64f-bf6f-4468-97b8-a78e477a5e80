#!/usr/bin/env python3
"""
Quick Nomic Model Downloader for Adrina AI Assistant v2.0

Simple script to download nomic-embed-text-v1 for offline embeddings.
Run this to enable 100% offline memory system.
"""

import os
import requests
from pathlib import Path
import sys

def download_with_progress(url: str, local_path: Path, description: str):
    """Download file with progress bar."""
    print(f"📥 Downloading {description}...")
    print(f"   From: {url}")
    print(f"   To: {local_path}")
    
    # Check if already exists
    if local_path.exists():
        print(f"   ✅ File already exists!")
        return True
    
    # Create directory
    local_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        mb_downloaded = downloaded / (1024 * 1024)
                        mb_total = total_size / (1024 * 1024)
                        print(f"\r   Progress: {progress:.1f}% ({mb_downloaded:.1f}/{mb_total:.1f} MB)", end='')
        
        print(f"\n   ✅ Downloaded successfully!")
        return True
        
    except Exception as e:
        print(f"\n   ❌ Download failed: {e}")
        if local_path.exists():
            local_path.unlink()
        return False

def download_nomic_model():
    """Download nomic-embed-text-v1 model."""
    print("🧠 Downloading nomic-embed-text-v1 for Offline Embeddings")
    print("=" * 60)
    print("Model: nomic-embed-text-v1")
    print("Size: ~500MB")
    print("Purpose: Local embeddings for memory system")
    print("Dimensions: 768")
    print("MTEB Score: 62.39 (excellent)")
    print()

    # Model configuration
    base_url = "https://huggingface.co/nomic-ai/nomic-embed-text-v1/resolve/main"
    model_dir = Path("models/nomic-embed-text")

    files_to_download = [
        ("config.json", "Model configuration"),
        ("pytorch_model.bin", "Model weights (~500MB)"),
        ("tokenizer.json", "Tokenizer configuration"),
        ("tokenizer_config.json", "Tokenizer settings"),
        ("special_tokens_map.json", "Special tokens mapping"),
        ("vocab.txt", "Vocabulary file"),
        ("config_sentence_transformers.json", "Sentence transformers config"),
        ("modules.json", "Module configuration"),
        ("sentence_bert_config.json", "Sentence BERT config"),
        ("README.md", "Model documentation")
    ]

    # Custom modeling files from nomic-bert-2048
    bert_base_url = "https://huggingface.co/nomic-ai/nomic-bert-2048/resolve/main"
    bert_files = [
        ("configuration_hf_nomic_bert.py", "Custom configuration class"),
        ("modeling_hf_nomic_bert.py", "Custom modeling class")
    ]

    print(f"📁 Download directory: {model_dir.absolute()}")
    print(f"📦 Files to download: {len(files_to_download) + len(bert_files)}")
    print()

    # Download main model files
    success_count = 0
    for file_name, description in files_to_download:
        url = f"{base_url}/{file_name}"
        local_path = model_dir / file_name

        if download_with_progress(url, local_path, f"{file_name} ({description})"):
            success_count += 1
        print()

    # Download custom modeling files
    for file_name, description in bert_files:
        url = f"{bert_base_url}/{file_name}"
        local_path = model_dir / file_name

        if download_with_progress(url, local_path, f"{file_name} ({description})"):
            success_count += 1
        print()

    # Summary
    total_files = len(files_to_download) + len(bert_files)
    print("📊 Download Summary:")
    print(f"   Files requested: {total_files}")
    print(f"   Files downloaded: {success_count}")

    if success_count == total_files:
        print("   ✅ nomic-embed-text-v1 downloaded successfully!")
        print()
        print("🎉 Offline embeddings ready!")
        print("   You can now use 100% offline memory system")
        print("   Test with: python memory/local_embedding_service.py")
        return True
    else:
        print("   ⚠️ Some files failed to download")
        print("   Try running the script again")
        return False

def verify_download():
    """Verify the download was successful."""
    print("\n🔍 Verifying download...")
    
    model_dir = Path("models/nomic-embed-text")
    required_files = ["config.json", "pytorch_model.bin", "tokenizer.json", "tokenizer_config.json",
                      "special_tokens_map.json", "vocab.txt"]
    
    all_present = True
    for file_name in required_files:
        file_path = model_dir / file_name
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"   ✅ {file_name} ({size_mb:.1f} MB)")
        else:
            print(f"   ❌ {file_name} - MISSING")
            all_present = False
    
    if all_present:
        print("   ✅ All files present!")
        
        # Try to test loading
        try:
            print("\n🧪 Testing model loading...")
            sys.path.append('.')
            from memory.local_embedding_service import LocalEmbeddingService
            
            service = LocalEmbeddingService()
            if service.is_available():
                print("   ✅ Model loads successfully!")
                
                # Test embedding
                embedding = service.generate_embedding("test")
                if embedding and len(embedding) == 768:
                    print(f"   ✅ Embedding generation works! (dimension: {len(embedding)})")
                    print("\n🎉 nomic-embed-text-v1 is ready for use!")
                    return True
                else:
                    print("   ❌ Embedding generation failed")
            else:
                print("   ❌ Model failed to load")
                
        except ImportError:
            print("   ⚠️ Cannot test loading (transformers not installed)")
            print("   Install with: pip install transformers torch")
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
    
    return all_present

def install_dependencies():
    """Install required dependencies."""
    import subprocess

    dependencies = ['einops']

    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} already installed")
        except ImportError:
            print(f"📦 Installing {dep}...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
                print(f"✅ {dep} installed successfully")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {dep}")
                return False

    return True

def main():
    """Main function."""
    print("🔒 Nomic Model Downloader for Offline Operation")
    print("=" * 50)

    # Check if requests is available
    try:
        import requests
    except ImportError:
        print("❌ Error: 'requests' library not found")
        print("Install with: pip install requests")
        return

    # Install dependencies
    print("🔧 Checking dependencies...")
    if not install_dependencies():
        print("❌ Failed to install required dependencies")
        return

    # Download the model
    if download_nomic_model():
        verify_download()
    else:
        print("\n❌ Download failed. Please check your internet connection and try again.")

if __name__ == "__main__":
    main()
