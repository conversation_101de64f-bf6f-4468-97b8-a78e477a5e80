#!/usr/bin/env python3
"""
Multi-Layer Command Processing System for Adrina AI Assistant v2.0

Implements intelligent command understanding with:
- Layer 1: Intent Recognition - What does user want?
- Layer 2: Task Decomposition - How to break it down?
- Layer 3: Priority Management - What order to execute?

Dynamic system that adapts to project context and user patterns.
"""

import os
import sys
import re
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from universal_logging.universal_logger import ComponentType, get_logger

class CommandType(Enum):
    """Types of commands the system can understand."""
    DIRECT_COMMAND = "direct_command"      # "create file", "delete folder"
    PRIORITY_COMMAND = "priority_command"  # "urgent: fix this", "high priority"
    TASK_COMMAND = "task_command"          # "add task", "complete task"
    QUERY_COMMAND = "query_command"        # "search for", "find information"
    SYSTEM_COMMAND = "system_command"      # "restart", "status", "help"
    CONVERSATIONAL = "conversational"     # Regular chat

class TaskPriority(Enum):
    """Task priority levels with numeric values for sorting."""
    CRITICAL = 1    # "urgent", "emergency", "critical", "broken"
    HIGH = 2        # "important", "high priority", "asap", "soon"
    MEDIUM = 3      # "when possible", "medium priority", "normal"
    LOW = 4         # "later", "low priority", "someday"
    BACKGROUND = 5  # "background task", "when idle", "no rush"

class ActionType(Enum):
    """Types of actions that can be performed."""
    CREATE = "create"           # Create files, functions, classes
    MODIFY = "modify"           # Update, change, refactor existing code
    DELETE = "delete"           # Remove files, functions, code
    ANALYZE = "analyze"         # Examine, review, understand code
    FIX = "fix"                # Resolve errors, bugs, issues
    OPTIMIZE = "optimize"       # Improve performance, efficiency
    DOCUMENT = "document"       # Add comments, documentation
    TEST = "test"              # Create tests, verify functionality
    CONFIGURE = "configure"     # Set up, adjust settings
    SEARCH = "search"          # Find information, code, files

@dataclass
class CommandIntent:
    """Represents the understood intent of a user command."""
    command_type: CommandType
    action_type: ActionType
    priority: TaskPriority
    target_objects: List[str]
    context_scope: str
    urgency_indicators: List[str]
    confidence: float
    reasoning: str

@dataclass
class TaskStep:
    """Individual step in a task decomposition."""
    step_number: int
    description: str
    action_type: ActionType
    estimated_effort: str  # "quick", "medium", "complex"
    dependencies: List[int]  # Step numbers this depends on
    target_files: List[str]
    success_criteria: str

@dataclass
class TaskPlan:
    """Complete task breakdown with steps and priorities."""
    task_id: str
    title: str
    description: str
    priority: TaskPriority
    steps: List[TaskStep]
    estimated_total_time: str
    complexity: str  # "simple", "moderate", "complex"
    requires_user_input: bool

@dataclass
class CommandAnalysis:
    """Complete analysis of a user command."""
    original_command: str
    intent: CommandIntent
    task_plan: Optional[TaskPlan]
    immediate_response: str
    requires_decomposition: bool
    processing_time: float

class MultiLayerCommandProcessor:
    """
    Intelligent command processor with three layers:
    1. Intent Recognition
    2. Task Decomposition  
    3. Priority Management
    """
    
    def __init__(self):
        self.logger = get_logger()
        self.project_context = self._load_project_context()
        self.command_patterns = self._initialize_command_patterns()
        self.priority_indicators = self._setup_priority_detection()
        self.action_patterns = self._setup_action_patterns()
        
        # Statistics
        self.stats = {
            'commands_processed': 0,
            'tasks_created': 0,
            'avg_processing_time': 0.0,
            'command_types': {ct.value: 0 for ct in CommandType},
            'priority_distribution': {tp.value: 0 for tp in TaskPriority}
        }
    
    def _load_project_context(self) -> Dict[str, Any]:
        """Load project context from PROJECT_DESCRIPTION.md."""
        try:
            project_desc_path = os.path.join(project_root, "PROJECT_DESCRIPTION.md")
            if os.path.exists(project_desc_path):
                with open(project_desc_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                return {
                    'project_description': content,
                    'components': self._extract_components(content),
                    'file_structure': self._extract_file_structure(content),
                    'capabilities': self._extract_capabilities(content),
                    'last_loaded': datetime.now()
                }
        except Exception as e:
            self.logger.log_warning(ComponentType.TASK_CLASSIFIER, f"Could not load project context: {e}")
        
        return {'project_description': '', 'components': [], 'file_structure': {}, 'capabilities': []}
    
    def _extract_components(self, content: str) -> List[str]:
        """Extract system components from project description."""
        components = []
        
        # Look for component mentions
        component_patterns = [
            r'MAIN CHATBOT.*?`([^`]+)`',
            r'TASK ASSISTANT.*?`([^`]+)`',
            r'MEMORY SYSTEM.*?`([^`]+)`',
            r'TTS SYSTEM.*?`([^`]+)`',
            r'ERROR SYSTEM.*?`([^`]+)`'
        ]
        
        for pattern in component_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            components.extend(matches)
        
        return components
    
    def _extract_file_structure(self, content: str) -> Dict[str, List[str]]:
        """Extract file structure from project description."""
        structure = {}
        
        # Look for folder structure
        folder_pattern = r'### ([^(]+) \(`([^`]+)` folder\):'
        file_pattern = r'- \*\*`([^`]+)`\*\*: (.+)'
        
        current_folder = None
        for line in content.split('\n'):
            folder_match = re.match(folder_pattern, line)
            if folder_match:
                current_folder = folder_match.group(2)
                structure[current_folder] = []
                continue
            
            if current_folder:
                file_match = re.match(file_pattern, line)
                if file_match:
                    structure[current_folder].append(file_match.group(1))
        
        return structure
    
    def _extract_capabilities(self, content: str) -> List[str]:
        """Extract system capabilities from project description."""
        capabilities = []
        
        # Look for capability lists
        capability_section = re.search(r'### ✅ WORKING FEATURES:(.*?)###', content, re.DOTALL)
        if capability_section:
            for line in capability_section.group(1).split('\n'):
                if line.strip().startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.')):
                    capability = re.sub(r'^\d+\.\s*\*\*([^*]+)\*\*:', r'\1', line.strip())
                    capabilities.append(capability)
        
        return capabilities
    
    def _initialize_command_patterns(self) -> Dict[str, Dict]:
        """Initialize command recognition patterns."""
        return {
            'file_operations': {
                'patterns': [
                    r'\b(create|make|generate|write|build)\s+(file|script|module|class|function)',
                    r'\b(delete|remove|rm)\s+(file|folder|directory)',
                    r'\b(modify|update|change|edit)\s+(file|code|function)'
                ],
                'action_type': ActionType.CREATE,
                'priority_boost': lambda text: 'urgent' in text.lower() or 'asap' in text.lower(),
                'scope_detection': self._detect_file_scope
            },
            
            'code_operations': {
                'patterns': [
                    r'\b(fix|resolve|debug|repair)\s+(error|bug|issue|problem)',
                    r'\b(refactor|optimize|improve|enhance)\s+(code|function|class|system)',
                    r'\b(add|implement|create)\s+(feature|functionality|method|class)'
                ],
                'action_type': ActionType.FIX,
                'priority_boost': lambda text: any(word in text.lower() for word in ['broken', 'critical', 'urgent', 'error']),
                'scope_detection': self._detect_code_scope
            },
            
            'system_operations': {
                'patterns': [
                    r'\b(configure|setup|install|initialize)\s+(system|assistant|component)',
                    r'\b(restart|reload|refresh|update)\s+(system|service|component)',
                    r'\b(test|verify|check|validate)\s+(system|functionality|feature)'
                ],
                'action_type': ActionType.CONFIGURE,
                'priority_boost': lambda text: 'critical' in text.lower() or 'important' in text.lower(),
                'scope_detection': self._detect_system_scope
            },
            
            'information_requests': {
                'patterns': [
                    r'\b(explain|describe|show|tell me about)\s+',
                    r'\b(what is|how does|how to|where is)\s+',
                    r'\b(find|search|look for|locate)\s+'
                ],
                'action_type': ActionType.ANALYZE,
                'priority_boost': lambda text: 'blocking' in text.lower() or 'need to know' in text.lower(),
                'scope_detection': self._detect_info_scope
            }
        }
    
    def _setup_priority_detection(self) -> Dict[TaskPriority, List[str]]:
        """Setup priority detection patterns."""
        return {
            TaskPriority.CRITICAL: [
                'urgent', 'emergency', 'critical', 'asap', 'immediately', 'now',
                'broken', 'not working', 'failing', 'error', 'bug', 'fix now'
            ],
            TaskPriority.HIGH: [
                'important', 'high priority', 'soon', 'quickly', 'fast',
                'needed', 'required', 'must have', 'should do'
            ],
            TaskPriority.MEDIUM: [
                'when possible', 'medium priority', 'normal', 'regular',
                'would like', 'could use', 'helpful'
            ],
            TaskPriority.LOW: [
                'later', 'low priority', 'someday', 'eventually', 'maybe',
                'nice to have', 'if time permits', 'when free'
            ],
            TaskPriority.BACKGROUND: [
                'background', 'when idle', 'no rush', 'whenever',
                'background task', 'low importance', 'optional'
            ]
        }
    
    def _setup_action_patterns(self) -> Dict[ActionType, List[str]]:
        """Setup action type detection patterns."""
        return {
            ActionType.CREATE: ['create', 'make', 'generate', 'build', 'write', 'add', 'implement'],
            ActionType.MODIFY: ['modify', 'update', 'change', 'edit', 'refactor', 'improve', 'enhance'],
            ActionType.DELETE: ['delete', 'remove', 'rm', 'clear', 'clean', 'purge'],
            ActionType.ANALYZE: ['analyze', 'examine', 'review', 'check', 'inspect', 'study'],
            ActionType.FIX: ['fix', 'resolve', 'debug', 'repair', 'solve', 'correct'],
            ActionType.OPTIMIZE: ['optimize', 'improve', 'enhance', 'speed up', 'make faster'],
            ActionType.DOCUMENT: ['document', 'comment', 'explain', 'describe', 'note'],
            ActionType.TEST: ['test', 'verify', 'validate', 'check', 'confirm'],
            ActionType.CONFIGURE: ['configure', 'setup', 'install', 'initialize', 'set up'],
            ActionType.SEARCH: ['search', 'find', 'look for', 'locate', 'discover']
        }
    
    def process_command(self, command: str, context: Dict[str, Any] = None) -> CommandAnalysis:
        """
        Process user command through all three layers.
        
        Args:
            command: User command string
            context: Optional context information
            
        Returns:
            Complete command analysis
        """
        start_time = time.time()
        
        try:
            # Layer 1: Intent Recognition
            intent = self._recognize_intent(command, context)
            
            # Layer 2: Task Decomposition (if needed)
            task_plan = None
            if self._requires_decomposition(intent):
                task_plan = self._decompose_task(intent, command)
            
            # Layer 3: Priority Management (handled in task creation)
            
            # Generate immediate response
            immediate_response = self._generate_immediate_response(intent, task_plan)
            
            processing_time = time.time() - start_time
            
            # Create analysis result
            analysis = CommandAnalysis(
                original_command=command,
                intent=intent,
                task_plan=task_plan,
                immediate_response=immediate_response,
                requires_decomposition=task_plan is not None,
                processing_time=processing_time
            )
            
            # Update statistics
            self._update_stats(analysis)
            
            # Log the analysis
            self.logger.log_system_event(
                f"Command processed: {intent.command_type.value}",
                {
                    'action_type': intent.action_type.value,
                    'priority': intent.priority.value,
                    'confidence': intent.confidence,
                    'processing_time': processing_time,
                    'requires_decomposition': task_plan is not None
                }
            )
            
            return analysis
            
        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, f"command processing: {command}")
            
            # Return fallback analysis
            return CommandAnalysis(
                original_command=command,
                intent=CommandIntent(
                    command_type=CommandType.CONVERSATIONAL,
                    action_type=ActionType.ANALYZE,
                    priority=TaskPriority.MEDIUM,
                    target_objects=[],
                    context_scope="unknown",
                    urgency_indicators=[],
                    confidence=0.1,
                    reasoning=f"Error in processing: {e}"
                ),
                task_plan=None,
                immediate_response=f"I encountered an error processing that command: {e}",
                requires_decomposition=False,
                processing_time=time.time() - start_time
            )

    def _recognize_intent(self, command: str, context: Dict[str, Any] = None) -> CommandIntent:
        """Layer 1: Recognize user intent from command."""
        command_lower = command.lower().strip()

        # Detect command type and action
        command_type = self._classify_command_type(command_lower)
        action_type = self._detect_action_type(command_lower)

        # Detect priority
        priority = self._detect_priority(command_lower)

        # Extract target objects
        target_objects = self._extract_targets(command_lower)

        # Determine context scope
        context_scope = self._determine_scope(command_lower, context)

        # Find urgency indicators
        urgency_indicators = self._find_urgency_indicators(command_lower)

        # Calculate confidence
        confidence = self._calculate_confidence(command_lower, command_type, action_type)

        # Generate reasoning
        reasoning = self._generate_reasoning(command_type, action_type, priority, confidence)

        return CommandIntent(
            command_type=command_type,
            action_type=action_type,
            priority=priority,
            target_objects=target_objects,
            context_scope=context_scope,
            urgency_indicators=urgency_indicators,
            confidence=confidence,
            reasoning=reasoning
        )

    def _classify_command_type(self, command: str) -> CommandType:
        """Classify the type of command."""
        # Check for system commands first
        system_commands = ['help', 'stats', 'status', 'quit', 'exit', 'clear', 'restart']
        if any(cmd in command for cmd in system_commands):
            return CommandType.SYSTEM_COMMAND

        # Check for priority indicators
        priority_words = ['urgent', 'critical', 'high priority', 'asap', 'immediately']
        if any(word in command for word in priority_words):
            return CommandType.PRIORITY_COMMAND

        # Check for task management
        task_words = ['task', 'todo', 'add to list', 'create task', 'manage']
        if any(word in command for word in task_words):
            return CommandType.TASK_COMMAND

        # Check for direct commands (actions)
        action_words = ['create', 'make', 'delete', 'fix', 'update', 'configure', 'install']
        if any(word in command for word in action_words):
            return CommandType.DIRECT_COMMAND

        # Check for queries
        query_words = ['what', 'how', 'where', 'when', 'why', 'explain', 'show', 'find']
        if any(word in command for word in query_words):
            return CommandType.QUERY_COMMAND

        return CommandType.CONVERSATIONAL

    def _detect_action_type(self, command: str) -> ActionType:
        """Detect the primary action type."""
        for action_type, keywords in self.action_patterns.items():
            if any(keyword in command for keyword in keywords):
                return action_type

        return ActionType.ANALYZE  # Default

    def _detect_priority(self, command: str) -> TaskPriority:
        """Detect priority level from command."""
        for priority, indicators in self.priority_indicators.items():
            if any(indicator in command for indicator in indicators):
                return priority

        return TaskPriority.MEDIUM  # Default

    def _extract_targets(self, command: str) -> List[str]:
        """Extract target objects from command."""
        targets = []

        # Common target patterns
        target_patterns = [
            r'\b(file|script|module|class|function|method)\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'\b([a-zA-Z_][a-zA-Z0-9_]*\.py)\b',
            r'\b(system|assistant|chatbot|memory|tts|error)\b',
            r'\b(the\s+)?([a-zA-Z_][a-zA-Z0-9_]*)\s+(system|component|module)\b'
        ]

        for pattern in target_patterns:
            matches = re.findall(pattern, command, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    targets.extend([m for m in match if m and m not in ['the', 'a', 'an']])
                else:
                    targets.append(match)

        return list(set(targets))  # Remove duplicates

    def _determine_scope(self, command: str, context: Dict[str, Any] = None) -> str:
        """Determine the scope of the command."""
        if 'system' in command or 'assistant' in command:
            return 'system_wide'
        elif any(word in command for word in ['file', 'script', 'module']):
            return 'file_level'
        elif any(word in command for word in ['function', 'method', 'class']):
            return 'code_level'
        elif any(word in command for word in ['project', 'entire', 'all']):
            return 'project_wide'
        else:
            return 'local'

    def _find_urgency_indicators(self, command: str) -> List[str]:
        """Find urgency indicators in command."""
        urgency_words = [
            'urgent', 'asap', 'immediately', 'now', 'quickly', 'fast',
            'critical', 'important', 'emergency', 'broken', 'failing'
        ]

        return [word for word in urgency_words if word in command]

    def _calculate_confidence(self, command: str, command_type: CommandType, action_type: ActionType) -> float:
        """Calculate confidence in the intent recognition."""
        confidence = 0.5  # Base confidence

        # Boost confidence for clear patterns
        if command_type != CommandType.CONVERSATIONAL:
            confidence += 0.2

        if action_type != ActionType.ANALYZE:
            confidence += 0.2

        # Boost for specific keywords
        specific_keywords = ['create', 'delete', 'fix', 'configure', 'urgent', 'important']
        keyword_matches = sum(1 for keyword in specific_keywords if keyword in command)
        confidence += min(keyword_matches * 0.1, 0.3)

        return min(confidence, 1.0)

    def _generate_reasoning(self, command_type: CommandType, action_type: ActionType,
                          priority: TaskPriority, confidence: float) -> str:
        """Generate human-readable reasoning for the classification."""
        parts = []
        parts.append(f"Command type: {command_type.value}")
        parts.append(f"Action: {action_type.value}")
        parts.append(f"Priority: {priority.value}")
        parts.append(f"Confidence: {confidence:.2f}")

        return " | ".join(parts)

    def _requires_decomposition(self, intent: CommandIntent) -> bool:
        """Determine if command requires task decomposition."""
        # Complex commands that need breaking down
        complex_actions = [ActionType.CONFIGURE, ActionType.CREATE, ActionType.MODIFY]
        complex_scopes = ['system_wide', 'project_wide']

        return (
            intent.action_type in complex_actions or
            intent.context_scope in complex_scopes or
            len(intent.target_objects) > 2 or
            intent.priority in [TaskPriority.CRITICAL, TaskPriority.HIGH]
        )

    def _decompose_task(self, intent: CommandIntent, original_command: str) -> TaskPlan:
        """Layer 2: Decompose complex commands into actionable tasks."""
        task_id = f"task_{int(time.time() * 1000) % 100000:05d}"

        # Generate task steps based on intent
        steps = self._generate_task_steps(intent, original_command)

        # Estimate complexity and time
        complexity = self._assess_complexity(steps)
        estimated_time = self._estimate_time(steps, complexity)

        # Determine if user input is needed
        requires_user_input = self._requires_user_input(intent, steps)

        return TaskPlan(
            task_id=task_id,
            title=self._generate_task_title(intent),
            description=self._generate_task_description(intent, original_command),
            priority=intent.priority,
            steps=steps,
            estimated_total_time=estimated_time,
            complexity=complexity,
            requires_user_input=requires_user_input
        )

    def _generate_task_steps(self, intent: CommandIntent, command: str) -> List[TaskStep]:
        """Generate specific task steps based on intent."""
        steps = []

        if intent.action_type == ActionType.CONFIGURE and 'assistant' in command.lower():
            # Example: "configure the assistant to understand commands"
            steps = [
                TaskStep(1, "Design command recognition patterns", ActionType.CREATE, "medium", [],
                        ["chatbot/command_processor.py"], "Patterns defined and documented"),
                TaskStep(2, "Implement priority detection system", ActionType.CREATE, "medium", [1],
                        ["chatbot/command_processor.py"], "Priority levels working correctly"),
                TaskStep(3, "Integrate with existing query identifier", ActionType.MODIFY, "complex", [2],
                        ["chatbot/query_identifier.py", "chatbot/multi_task_assistant.py"], "Integration complete and tested"),
                TaskStep(4, "Test command understanding", ActionType.TEST, "quick", [3],
                        ["test_commands.py"], "All test cases passing"),
                TaskStep(5, "Update documentation", ActionType.DOCUMENT, "quick", [4],
                        ["README.md", "PROJECT_DESCRIPTION.md"], "Documentation updated")
            ]

        elif intent.action_type == ActionType.CREATE and any(target in ['file', 'script', 'module'] for target in intent.target_objects):
            # File creation tasks
            steps = [
                TaskStep(1, "Design file structure and interface", ActionType.ANALYZE, "quick", [],
                        [], "Structure planned and documented"),
                TaskStep(2, "Create the file with basic structure", ActionType.CREATE, "medium", [1],
                        [f"{intent.target_objects[0] if intent.target_objects else 'new_file'}.py"], "File created with skeleton"),
                TaskStep(3, "Implement core functionality", ActionType.CREATE, "complex", [2],
                        [f"{intent.target_objects[0] if intent.target_objects else 'new_file'}.py"], "Core features working"),
                TaskStep(4, "Add error handling and validation", ActionType.MODIFY, "medium", [3],
                        [f"{intent.target_objects[0] if intent.target_objects else 'new_file'}.py"], "Error handling complete"),
                TaskStep(5, "Test and validate functionality", ActionType.TEST, "medium", [4],
                        [], "All functionality tested and working")
            ]

        elif intent.action_type == ActionType.FIX:
            # Error fixing tasks
            steps = [
                TaskStep(1, "Identify and analyze the error", ActionType.ANALYZE, "quick", [],
                        [], "Error root cause identified"),
                TaskStep(2, "Design solution approach", ActionType.ANALYZE, "medium", [1],
                        [], "Solution strategy documented"),
                TaskStep(3, "Implement the fix", ActionType.MODIFY, "medium", [2],
                        intent.target_objects, "Fix implemented"),
                TaskStep(4, "Test the fix", ActionType.TEST, "quick", [3],
                        [], "Fix verified working"),
                TaskStep(5, "Update related documentation", ActionType.DOCUMENT, "quick", [4],
                        [], "Documentation updated if needed")
            ]

        else:
            # Generic task breakdown
            steps = [
                TaskStep(1, f"Analyze requirements for {intent.action_type.value}", ActionType.ANALYZE, "quick", [],
                        [], "Requirements understood"),
                TaskStep(2, f"Plan {intent.action_type.value} approach", ActionType.ANALYZE, "medium", [1],
                        [], "Approach planned"),
                TaskStep(3, f"Execute {intent.action_type.value}", intent.action_type, "medium", [2],
                        intent.target_objects, "Action completed"),
                TaskStep(4, "Verify results", ActionType.TEST, "quick", [3],
                        [], "Results verified")
            ]

        return steps

    def _assess_complexity(self, steps: List[TaskStep]) -> str:
        """Assess overall task complexity."""
        complex_count = sum(1 for step in steps if step.estimated_effort == "complex")
        medium_count = sum(1 for step in steps if step.estimated_effort == "medium")

        if complex_count > 1 or len(steps) > 5:
            return "complex"
        elif complex_count == 1 or medium_count > 2:
            return "moderate"
        else:
            return "simple"

    def _estimate_time(self, steps: List[TaskStep], complexity: str) -> str:
        """Estimate total time for task completion."""
        effort_times = {"quick": 15, "medium": 45, "complex": 120}  # minutes

        total_minutes = sum(effort_times.get(step.estimated_effort, 30) for step in steps)

        if total_minutes < 30:
            return "< 30 minutes"
        elif total_minutes < 120:
            return f"~{total_minutes // 60} hours"
        else:
            return f"~{total_minutes // 60} hours (complex task)"

    def _requires_user_input(self, intent: CommandIntent, steps: List[TaskStep]) -> bool:
        """Determine if task requires user input."""
        return (
            intent.priority == TaskPriority.CRITICAL or
            intent.context_scope in ['system_wide', 'project_wide'] or
            any('design' in step.description.lower() for step in steps)
        )

    def _generate_task_title(self, intent: CommandIntent) -> str:
        """Generate a clear task title."""
        action = intent.action_type.value.title()
        targets = ", ".join(intent.target_objects[:2]) if intent.target_objects else "System"
        return f"{action} {targets}"

    def _generate_task_description(self, intent: CommandIntent, original_command: str) -> str:
        """Generate detailed task description."""
        return f"Task created from command: '{original_command}'\nAction: {intent.action_type.value}\nScope: {intent.context_scope}\nPriority: {intent.priority.value}"

    def _generate_immediate_response(self, intent: CommandIntent, task_plan: Optional[TaskPlan]) -> str:
        """Generate immediate response to user."""
        if task_plan:
            return f"I understand you want to {intent.action_type.value} {', '.join(intent.target_objects) if intent.target_objects else 'the system'}. I've created a {task_plan.complexity} task plan with {len(task_plan.steps)} steps (estimated time: {task_plan.estimated_total_time}). Priority: {intent.priority.value}."
        else:
            return f"I understand you want to {intent.action_type.value} {', '.join(intent.target_objects) if intent.target_objects else 'something'}. This appears to be a {intent.priority.value} priority {intent.command_type.value}."

    def _update_stats(self, analysis: CommandAnalysis):
        """Update processing statistics."""
        self.stats['commands_processed'] += 1
        self.stats['command_types'][analysis.intent.command_type.value] += 1
        self.stats['priority_distribution'][analysis.intent.priority.value] += 1

        if analysis.task_plan:
            self.stats['tasks_created'] += 1

        # Update average processing time
        total_time = self.stats['avg_processing_time'] * (self.stats['commands_processed'] - 1)
        self.stats['avg_processing_time'] = (total_time + analysis.processing_time) / self.stats['commands_processed']

    def get_stats(self) -> Dict[str, Any]:
        """Get command processor statistics."""
        return dict(self.stats)

    # Scope detection helper methods
    def _detect_file_scope(self, command: str) -> str:
        return "file_level"

    def _detect_code_scope(self, command: str) -> str:
        return "code_level"

    def _detect_system_scope(self, command: str) -> str:
        return "system_wide"

    def _detect_info_scope(self, command: str) -> str:
        return "information"

# Test function
def test_command_processor():
    """Test the command processor with various commands."""
    processor = MultiLayerCommandProcessor()

    test_commands = [
        "configure the assistant to understand user commands and all tasks priority",
        "urgent: fix the TTS error in the system",
        "create a new file for handling user preferences",
        "what is the current system status?",
        "help me understand how the memory system works",
        "high priority: optimize the query classification performance",
        "when you have time, add documentation to the chatbot module"
    ]

    print("🧪 Testing Multi-Layer Command Processor")
    print("=" * 70)

    for i, command in enumerate(test_commands, 1):
        print(f"\n📝 Test {i}: '{command}'")

        analysis = processor.process_command(command)

        print(f"🎯 Intent: {analysis.intent.command_type.value} | {analysis.intent.action_type.value}")
        print(f"⚡ Priority: {analysis.intent.priority.value} | Confidence: {analysis.intent.confidence:.2f}")
        print(f"🎯 Targets: {', '.join(analysis.intent.target_objects) if analysis.intent.target_objects else 'None'}")
        print(f"📋 Task Plan: {'Yes' if analysis.task_plan else 'No'}")
        if analysis.task_plan:
            print(f"   📊 Complexity: {analysis.task_plan.complexity} | Steps: {len(analysis.task_plan.steps)} | Time: {analysis.task_plan.estimated_total_time}")
        print(f"💬 Response: {analysis.immediate_response}")
        print(f"⏱️ Processing: {analysis.processing_time:.3f}s")

    print(f"\n📊 Final Statistics:")
    stats = processor.get_stats()
    print(f"Commands processed: {stats['commands_processed']}")
    print(f"Tasks created: {stats['tasks_created']}")
    print(f"Avg processing time: {stats['avg_processing_time']:.3f}s")
    print(f"Command types: {stats['command_types']}")
    print(f"Priority distribution: {stats['priority_distribution']}")

    print("\n✅ Command processor test completed!")

if __name__ == "__main__":
    test_command_processor()
