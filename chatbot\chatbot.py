from llama_cpp import Llam<PERSON>  # Import the Llama model implementation
import sys
from config.main_chatbot_config import (
    LLAMA_MODEL_PATH, CONTEXT_WINDOW_SIZE, CPU_THREADS,
    MAX_TOKENS, TEMPERATURE, TOP_P, REPEAT_PENALTY, TOP_K, MIN_P, TYPICAL_P,
    SYSTEM_PROMPT, USE_GPU, N_GPU_LAYERS, GPU_MEMORY_FRACTION, MAIN_GPU,
    GPU_BATCH_SIZE, USE_MMAP, USE_MLOCK, NUMA
)

class Chatbot:
    """A chatbot class that uses the Llama model to generate conversational responses"""
    
    def __init__(self, model_path):
        """Initialize the chatbot with a specific model
        
        Args:
            model_path (str): Path to the GGUF model file
        """
        try:
            # Initialize the Llama model with GPU acceleration
            if USE_GPU:
                print("🚀 Initializing with GPU acceleration...")
                self.llm = Llama(
                    model_path=model_path,
                    n_ctx=CONTEXT_WINDOW_SIZE,
                    n_threads=CPU_THREADS,
                    n_gpu_layers=N_GPU_LAYERS,  # Use GPU for all layers (-1 = auto-detect)
                    main_gpu=MAIN_GPU,  # Primary GPU device
                    n_batch=GPU_BATCH_SIZE,  # Optimized batch size for RTX 4070 Ti
                    use_mmap=USE_MMAP,  # Memory-mapped file access
                    use_mlock=USE_MLOCK,  # Lock model in RAM
                    numa=NUMA,  # NUMA optimization
                    verbose=True  # Show GPU initialization details
                )
                print("✅ GPU acceleration enabled!")
            else:
                print("🖥️ Initializing with CPU only...")
                self.llm = Llama(
                    model_path=model_path,
                    n_ctx=CONTEXT_WINDOW_SIZE,
                    n_threads=CPU_THREADS,
                    n_batch=GPU_BATCH_SIZE,  # Same batch size for consistency
                    use_mmap=USE_MMAP,  # Memory-mapped file access
                    use_mlock=USE_MLOCK,  # Lock model in RAM
                    numa=NUMA  # NUMA optimization
                )
                print("✅ CPU-only mode enabled!")
            
            # Use system prompt from config
            self.system_prompt = SYSTEM_PROMPT
            
            # Initialize conversation history with the system prompt
            self.conversation_history = [{"role": "system", "content": self.system_prompt}]
        except Exception as e:
            print(f"Error loading model: {e}")
            sys.exit(1)

    def generate_response_stream(self, prompt, max_tokens=256):
        """Generate a response to the user's input and stream it token by token
        
        Args:
            prompt (str): The user's input text
            max_tokens (int): Maximum number of tokens to generate
            
        Yields:
            str: Generated text chunks as they are produced
        """
        try:
            # Add user message to conversation history
            self.conversation_history.append({"role": "user", "content": prompt})
            
            # Format the full conversation history into a prompt string
            formatted_prompt = self.system_prompt + "\n\n"  # Start with system prompt
            
            # Only include the last few turns of conversation
            recent_messages = self.conversation_history[-4:]  # Keep last 4 turns
            for message in recent_messages:
                if message["role"] == "user":
                    formatted_prompt += f"Human: {message['content']}\n"
                elif message["role"] == "assistant":
                    formatted_prompt += f"Adrina: {message['content']}\n"

            # Add final prompt
            formatted_prompt += "Adrina:"
            
            # Stream the response token by token
            response_text = ""
            for token in self.llm(
                prompt=formatted_prompt,
                max_tokens=max_tokens,
                temperature=TEMPERATURE,
                top_p=TOP_P,
                top_k=TOP_K,  # Limit vocabulary for faster sampling
                min_p=MIN_P,  # Minimum probability threshold
                typical_p=TYPICAL_P,  # Typical sampling (1.0 = disabled)
                repeat_penalty=REPEAT_PENALTY,
                echo=False,
                stream=True,
                stop=["User:", "Human:", "Assistant:", "Adrina:", "\nUser:", "\nHuman:", "\nAssistant:", "\nAdrina:"]
            ):
                if isinstance(token, dict) and 'choices' in token and len(token['choices']) > 0:
                    chunk = token['choices'][0].get('text', '')
                    response_text += chunk
                    yield chunk
            
            # Update conversation history with the complete response
            self.conversation_history.append({"role": "assistant", "content": response_text.strip()})
            
        except Exception as e:
            yield f"Error generating response: {e}"
    
    def generate_response(self, prompt, max_tokens=256):
        """Generate a complete response to the user's input
        
        Args:
            prompt (str): The user's input text
            max_tokens (int): Maximum number of tokens to generate
            
        Returns:
            str: The complete generated response
        """
        try:
            # Use the streaming version and collect all chunks
            return ''.join([chunk for chunk in self.generate_response_stream(prompt, max_tokens)])
        except Exception as e:
            return f"Error generating response: {e}"

    def chat(self):
        """Start an interactive chat session with the user"""
        # Display initialization message
        print("Chatbot initialized. Type 'quit' to exit.")
        print(f"System Prompt: {self.system_prompt}\n")
        
        # Main conversation loop
        while True:
            # Get user input
            user_input = input("\nYou: ").strip()
            
            # Check for quit command
            if user_input.lower() == 'quit':
                break
            
            # Generate and display response
            response = self.generate_response(user_input)

if __name__ == "__main__":
    # Create and start the chatbot using model path from config
    chatbot = Chatbot(LLAMA_MODEL_PATH)
    chatbot.chat()