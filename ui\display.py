#!/usr/bin/env python3
"""
Display utilities for Adrina AI Assistant v2.0

Contains all UI display functions including banners, help text, and statistics formatting.
"""

import os

def clear_screen():
    """Clear the console screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_banner():
    """Print the Adrina banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║              🤖 ADRINA AI ASSISTANT v2.0 🤖                  ║
    ║                                                              ║
    ║              Multi-Task Intelligence System                  ║
    ║                                                              ║
    ║  🧠 Dual-LLM Architecture  ⚡ GPU Accelerated               ║
    ║  🎯 Perfect Classification  🔊 Real-time TTS                ║
    ║  💾 Persistent Memory      🚀 Lightning Fast                ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_help():
    """Print available commands."""
    help_text = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                         COMMANDS                             ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  💬 Just type your message - Adrina will respond             ║
    ║                                                              ║
    ║  📊 stats     - Show performance statistics                 ║
    ║  🧠 memory    - Show memory system status                   ║
    ║  🧪 test      - Run classification tests                    ║
    ║  🚨 errors    - Show system error summary                   ║
    ║  🎯 command   - Test command understanding system           ║
    ║  🔄 clear     - Clear the screen                            ║
    ║  ❓ help      - Show this help message                      ║
    ║  👋 quit/exit - Exit the assistant                          ║
    ║                                                              ║
    ║  🎯 Query Types:                                            ║
    ║     FAST   - Direct questions (What is Python?)            ║
    ║     MEMORY - Context questions (What did we discuss?)      ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(help_text)

def format_stats(stats):
    """Format statistics for display."""
    if 'processor' not in stats:
        return "📊 No statistics available yet."
    
    proc_stats = stats['processor']
    system_stats = stats.get('system', {})
    
    output = f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    PERFORMANCE STATISTICS                    ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  📈 Query Processing:                                        ║
    ║     Total Queries: {proc_stats['total_queries']:<10} Classification Accuracy: {proc_stats['classification_accuracy']:.1f}%  ║
    ║     Fast Path:     {proc_stats['fast_path_count']:<10} ({proc_stats.get('fast_percentage', 0):.1f}%)                    ║
    ║     Memory Path:   {proc_stats['memory_path_count']:<10} ({proc_stats.get('memory_percentage', 0):.1f}%)                    ║
    ║                                                              ║
    ║  ⚡ Performance Metrics:                                     ║
    ║     Avg Classification: {proc_stats.get('avg_classification_time', 0):.3f}s                        ║
    ║     Avg Fast Path:      {proc_stats.get('avg_fast_path_time', 0):.3f}s                        ║
    ║     Avg Memory Path:    {proc_stats.get('avg_memory_path_time', 0):.3f}s                        ║
    ║                                                              ║
    ║  🔧 System Status:                                           ║
    ║     Multi-Task Assistant: {'✅ Loaded' if proc_stats.get('assistant_loaded') else '❌ Not Loaded':<20}        ║
    ║     Speech Queue Size:    {system_stats.get('speech_queue_size', 0):<20}        ║
    ║     Currently Speaking:   {'🔊 Yes' if system_stats.get('is_speaking') else '🔇 No':<20}        ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    return output

def format_memory_stats(stats):
    """Format memory statistics for display."""
    if 'memory' not in stats or stats['memory'].get('status') == 'unavailable':
        return "🧠 Memory system statistics unavailable."
    
    memory_stats = stats['memory']
    
    output = f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                      MEMORY SYSTEM STATUS                   ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  📚 Memory Database:                                         ║
    ║     Total Memories: {memory_stats.get('total_memories', 0):<20}                    ║
    ║     Conversations:  {memory_stats.get('conversation_count', 0):<20}                    ║
    ║     Facts:          {memory_stats.get('fact_count', 0):<20}                    ║
    ║     Preferences:    {memory_stats.get('preference_count', 0):<20}                    ║
    ║                                                              ║
    ║  🔧 System Health:                                           ║
    ║     Embedding Service: {'✅ Active' if memory_stats.get('embedding_service_active') else '❌ Inactive':<20}           ║
    ║     Database Status:   {'✅ Connected' if memory_stats.get('database_connected') else '❌ Disconnected':<20}           ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    return output
