#!/usr/bin/env python3
"""
Offline Memory Manager for Adrina AI Assistant v2.0

100% offline memory system using local nomic embeddings instead of Ollama.
Drop-in replacement for the original MemoryManager.
"""

import os
import sys
import logging
from typing import List, Dict, Any, Optional, Tuple

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from memory.local_embedding_service import LocalEmbeddingService
from memory.chroma_storage import ChromaDBStorage
from config.memory_config import (
    MIN_TEXT_LENGTH, MAX_TEXT_LENGTH, SIMILARITY_THRESHOLD,
    DEFAULT_MAX_RESULTS, CONTEXT_MAX_ITEMS
)

class OfflineMemoryManager:
    """
    100% offline memory manager using local nomic embeddings.
    
    Drop-in replacement for MemoryManager that works without <PERSON>llama.
    """
    
    def __init__(self,
                 persist_directory: str = "./memory/memory_db",
                 collection_name: str = "adrina_memories",
                 model_path: str = "models/nomic-embed-text"):
        """
        Initialize offline memory manager.
        
        Args:
            persist_directory: Directory for ChromaDB persistence
            collection_name: ChromaDB collection name
            model_path: Path to local nomic model
        """
        self.logger = logging.getLogger(__name__)
        
        # Initialize local embedding service
        self.embedding_service = LocalEmbeddingService(model_path=model_path)
        
        # Initialize storage
        self.storage = ChromaDBStorage(
            persist_directory=persist_directory,
            collection_name=collection_name
        )
        
        # Memory validation settings
        self.min_text_length = MIN_TEXT_LENGTH
        self.max_text_length = MAX_TEXT_LENGTH
        self.similarity_threshold = SIMILARITY_THRESHOLD
        
        # Check if system is ready
        self.is_available = self.embedding_service.is_available()
        
        if self.is_available:
            self.logger.info("✅ Offline memory system initialized successfully")
        else:
            self.logger.warning("⚠️ Offline memory system not available - local model not found")
    
    def store_memory(self, 
                    text: str, 
                    memory_type: str = "conversation",
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Store a memory in the offline system.
        
        Args:
            text: Text content to store
            memory_type: Type of memory (conversation, fact, preference, etc.)
            metadata: Additional metadata
            
        Returns:
            True if stored successfully, False otherwise
        """
        if not self.is_available:
            self.logger.warning("Offline memory system not available")
            return False
        
        # Validate text
        if not self._is_valid_memory_text(text):
            return False
        
        try:
            # Generate embedding
            embedding = self.embedding_service.generate_embedding(text)
            if not embedding:
                self.logger.error("Failed to generate embedding for memory")
                return False
            
            # Store in ChromaDB
            memory_id = self.storage.add_memory(
                text=text,
                embedding=embedding,
                memory_type=memory_type,
                metadata=metadata
            )
            
            if memory_id:
                self.logger.debug(f"Stored memory: {text[:50]}... (ID: {memory_id})")
                return True
            else:
                self.logger.error("Failed to store memory in database")
                return False
                
        except Exception as e:
            self.logger.error(f"Error storing memory: {e}")
            return False
    
    def search_memories(self, 
                       query: str, 
                       memory_types: Optional[List[str]] = None,
                       max_results: int = DEFAULT_MAX_RESULTS,
                       similarity_threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Search for relevant memories.
        
        Args:
            query: Search query
            memory_types: Filter by memory types
            max_results: Maximum number of results
            similarity_threshold: Minimum similarity score
            
        Returns:
            List of relevant memories
        """
        if not self.is_available:
            self.logger.warning("Offline memory system not available")
            return []
        
        try:
            # Generate query embedding
            query_embedding = self.embedding_service.generate_embedding(query)
            if not query_embedding:
                self.logger.error("Failed to generate embedding for search query")
                return []
            
            # Search in ChromaDB
            results = self.storage.search_memories(
                query_embedding=query_embedding,
                n_results=max_results,
                memory_types=memory_types
            )
            
            # Filter by similarity threshold
            threshold = similarity_threshold or self.similarity_threshold
            filtered_results = []
            
            for result in results:
                if result.get('similarity', 0) >= threshold:
                    filtered_results.append(result)
            
            self.logger.debug(f"Found {len(filtered_results)} relevant memories for query: {query[:50]}...")
            return filtered_results
            
        except Exception as e:
            self.logger.error(f"Error searching memories: {e}")
            return []
    
    def get_context_for_query(self, 
                             query: str, 
                             max_items: int = CONTEXT_MAX_ITEMS) -> str:
        """
        Get contextual information for a query.
        
        Args:
            query: User query
            max_items: Maximum context items to include
            
        Returns:
            Formatted context string
        """
        if not self.is_available:
            return ""
        
        try:
            # Search for relevant memories
            memories = self.search_memories(query, max_results=max_items)
            
            if not memories:
                return ""
            
            # Format context
            context_parts = []
            for memory in memories[:max_items]:
                text = memory.get('text', '')
                memory_type = memory.get('memory_type', 'unknown')
                similarity = memory.get('similarity', 0)
                
                context_parts.append(f"[{memory_type.upper()}] {text}")
            
            context = "\n".join(context_parts)
            self.logger.debug(f"Generated context with {len(memories)} items")
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error generating context: {e}")
            return ""
    
    def store_conversation(self, user_input: str, ai_response: str) -> bool:
        """Store a conversation exchange."""
        conversation_text = f"User: {user_input}\nAdrina: {ai_response}"
        return self.store_memory(
            text=conversation_text,
            memory_type="conversation",
            metadata={
                "user_input": user_input,
                "ai_response": ai_response
            }
        )
    
    def store_fact(self, fact: str, category: str = "general") -> bool:
        """Store a factual piece of information."""
        return self.store_memory(
            text=fact,
            memory_type="fact",
            metadata={"category": category}
        )
    
    def store_preference(self, preference: str, category: str = "general") -> bool:
        """Store a user preference."""
        return self.store_memory(
            text=preference,
            memory_type="preference",
            metadata={"category": category}
        )
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory system statistics."""
        try:
            storage_stats = self.storage.get_memory_stats()
            embedding_stats = self.embedding_service.get_stats()
            
            return {
                "offline_memory_available": self.is_available,
                "storage": storage_stats,
                "embeddings": embedding_stats,
                "model_info": self.embedding_service.get_model_info()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting memory stats: {e}")
            return {"offline_memory_available": False, "error": str(e)}
    
    def _is_valid_memory_text(self, text: str) -> bool:
        """Validate if text is suitable for memory storage."""
        if not text or not text.strip():
            return False
        
        text_length = len(text.strip())
        
        if text_length < self.min_text_length:
            self.logger.debug(f"Text too short for memory: {text_length} < {self.min_text_length}")
            return False
        
        if text_length > self.max_text_length:
            self.logger.debug(f"Text too long for memory: {text_length} > {self.max_text_length}")
            return False
        
        # Additional validation rules
        text_lower = text.lower().strip()
        
        # Skip very generic responses
        generic_responses = [
            "i don't know", "i'm not sure", "maybe", "perhaps",
            "hello", "hi", "bye", "goodbye", "thanks", "thank you"
        ]
        
        if text_lower in generic_responses:
            return False
        
        return True
    
    def clear_memories(self, memory_type: Optional[str] = None) -> bool:
        """Clear memories of a specific type or all memories."""
        try:
            if memory_type:
                # Clear specific type (would need ChromaDB implementation)
                self.logger.warning("Clearing specific memory types not implemented yet")
                return False
            else:
                # Clear all memories
                self.storage.clear_all_memories()
                self.logger.info("Cleared all memories")
                return True
                
        except Exception as e:
            self.logger.error(f"Error clearing memories: {e}")
            return False

# Test function
def test_offline_memory():
    """Test the offline memory manager."""
    print("🧪 Testing Offline Memory Manager")
    print("=" * 50)
    
    manager = OfflineMemoryManager()
    
    if not manager.is_available:
        print("❌ Offline memory system not available")
        print("Download model with:")
        print("huggingface-cli download nomic-ai/nomic-embed-text-v1 --local-dir models/nomic-embed-text")
        return
    
    print("✅ Offline memory system available")
    
    # Test storing memories
    print("\n📝 Testing memory storage...")
    
    success1 = manager.store_fact("Python is a programming language", "programming")
    success2 = manager.store_preference("I prefer dark mode interfaces", "ui")
    success3 = manager.store_conversation("What is AI?", "AI stands for Artificial Intelligence")
    
    print(f"Stored fact: {'✅' if success1 else '❌'}")
    print(f"Stored preference: {'✅' if success2 else '❌'}")
    print(f"Stored conversation: {'✅' if success3 else '❌'}")
    
    # Test searching
    print("\n🔍 Testing memory search...")
    
    results = manager.search_memories("programming language")
    print(f"Found {len(results)} results for 'programming language'")
    
    for i, result in enumerate(results):
        print(f"  {i+1}. {result.get('text', '')[:50]}... (similarity: {result.get('similarity', 0):.3f})")
    
    # Test context generation
    print("\n🧠 Testing context generation...")
    
    context = manager.get_context_for_query("What programming languages do you know?")
    if context:
        print(f"Generated context:\n{context}")
    else:
        print("No context generated")
    
    # Show stats
    print("\n📊 Memory Statistics:")
    stats = manager.get_memory_stats()
    print(f"Available: {stats.get('offline_memory_available')}")
    print(f"Total memories: {stats.get('storage', {}).get('total_memories', 0)}")
    print(f"Embeddings generated: {stats.get('embeddings', {}).get('total_embeddings', 0)}")
    
    print("\n✅ Offline memory test completed!")

if __name__ == "__main__":
    test_offline_memory()
