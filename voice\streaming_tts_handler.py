#!/usr/bin/env python3
"""
Streaming TTS Handler for Adrina AI Assistant v2.0

Provides real-time text-to-speech conversion that starts speaking
as soon as the first words are generated, not waiting for complete response.
"""

import re
import time
import threading
from queue import Queue, Empty
from typing import Optional, Callable, List
from dataclasses import dataclass

@dataclass
class TTSChunk:
    """A chunk of text ready for TTS processing."""
    text: str
    chunk_id: int
    is_final: bool = False
    priority: int = 0  # 0 = normal, 1 = high priority

class StreamingTTSHandler:
    """Handles real-time streaming TTS with immediate audio generation."""
    
    def __init__(self, tts_manager, min_chunk_length: int = 3, max_chunk_length: int = 50):
        self.tts_manager = tts_manager
        self.min_chunk_length = min_chunk_length  # Minimum words before speaking
        self.max_chunk_length = max_chunk_length  # Maximum words per chunk
        
        # Streaming state
        self.active_streams = {}  # query_id -> stream_state
        self.tts_queue = Queue()
        self.tts_thread = None
        self.shutdown_event = threading.Event()
        
        # Text processing
        self.sentence_endings = ['.', '!', '?', '\n']
        self.pause_markers = [',', ';', ':', '-', '—']
        
        # Start TTS processing thread
        self._start_tts_thread()
    
    def _start_tts_thread(self):
        """Start the background TTS processing thread."""
        self.tts_thread = threading.Thread(
            target=self._tts_worker,
            daemon=True,
            name="StreamingTTS"
        )
        self.tts_thread.start()
    
    def _tts_worker(self):
        """Background worker that processes TTS chunks."""
        while not self.shutdown_event.is_set():
            try:
                # Get TTS chunk with timeout
                chunk = self.tts_queue.get(timeout=1.0)
                if chunk is None:  # Shutdown signal
                    break
                
                # Process the TTS chunk
                self._process_tts_chunk(chunk)
                self.tts_queue.task_done()
                
            except Empty:
                continue
            except Exception as e:
                print(f"Error in TTS worker: {e}")
    
    def _process_tts_chunk(self, chunk: TTSChunk):
        """Process a single TTS chunk."""
        try:
            if chunk.text.strip():
                # Generate audio for this chunk
                print(f"🎤 TTS: Speaking chunk {chunk.chunk_id}: '{chunk.text[:30]}...'")
                
                # Use existing TTS manager
                if self.tts_manager:
                    self.tts_manager.speak(chunk.text)
                
        except Exception as e:
            print(f"Error processing TTS chunk {chunk.chunk_id}: {e}")
    
    def start_streaming_response(self, query_id: str) -> 'StreamingResponseHandler':
        """
        Start a new streaming response session.
        
        Args:
            query_id: Unique identifier for this response
            
        Returns:
            StreamingResponseHandler for this session
        """
        handler = StreamingResponseHandler(
            query_id=query_id,
            tts_handler=self,
            min_chunk_length=self.min_chunk_length,
            max_chunk_length=self.max_chunk_length
        )
        
        self.active_streams[query_id] = handler
        return handler
    
    def queue_tts_chunk(self, chunk: TTSChunk):
        """Queue a TTS chunk for processing."""
        self.tts_queue.put(chunk)
    
    def complete_streaming_response(self, query_id: str):
        """Complete a streaming response session."""
        if query_id in self.active_streams:
            handler = self.active_streams[query_id]
            handler.finalize()
            del self.active_streams[query_id]
    
    def shutdown(self):
        """Shutdown the streaming TTS handler."""
        self.shutdown_event.set()
        
        # Signal shutdown to TTS thread
        self.tts_queue.put(None)
        
        # Wait for thread to finish
        if self.tts_thread and self.tts_thread.is_alive():
            self.tts_thread.join(timeout=5)

class StreamingResponseHandler:
    """Handles streaming text input and converts to TTS chunks."""
    
    def __init__(self, query_id: str, tts_handler: StreamingTTSHandler, 
                 min_chunk_length: int = 3, max_chunk_length: int = 50):
        self.query_id = query_id
        self.tts_handler = tts_handler
        self.min_chunk_length = min_chunk_length
        self.max_chunk_length = max_chunk_length
        
        # State tracking
        self.buffer = ""
        self.chunk_counter = 0
        self.word_count = 0
        self.last_chunk_time = time.time()
        self.finalized = False
        
        # Timing thresholds
        self.max_wait_time = 2.0  # Max seconds to wait before forcing a chunk
        self.min_wait_time = 0.5  # Min seconds between chunks
    
    def add_text(self, text: str):
        """
        Add streaming text to the handler.
        
        Args:
            text: New text chunk from the chatbot
        """
        if self.finalized:
            return
        
        self.buffer += text
        self.word_count = len(self.buffer.split())
        
        # Check if we should create a TTS chunk
        should_chunk = self._should_create_chunk()
        
        if should_chunk:
            self._create_and_send_chunk()
    
    def _should_create_chunk(self) -> bool:
        """Determine if we should create a TTS chunk now."""
        current_time = time.time()
        time_since_last = current_time - self.last_chunk_time
        
        # Force chunk if we've waited too long
        if time_since_last >= self.max_wait_time and self.word_count >= 1:
            return True
        
        # Don't chunk too frequently
        if time_since_last < self.min_wait_time:
            return False
        
        # Check for natural break points
        if self._has_natural_break():
            return True
        
        # Check word count thresholds
        if self.word_count >= self.max_chunk_length:
            return True
        
        if self.word_count >= self.min_chunk_length and time_since_last >= 1.0:
            return True
        
        return False
    
    def _has_natural_break(self) -> bool:
        """Check if buffer ends with a natural speaking break."""
        if not self.buffer:
            return False
        
        # Check for sentence endings
        if any(self.buffer.rstrip().endswith(ending) for ending in ['.', '!', '?']):
            return True
        
        # Check for pause markers with following space
        if any(f"{marker} " in self.buffer[-3:] for marker in [',', ';', ':']):
            return True
        
        return False
    
    def _create_and_send_chunk(self):
        """Create a TTS chunk from current buffer and send it."""
        if not self.buffer.strip():
            return
        
        # Find the best break point
        chunk_text = self._extract_chunk_text()
        
        if chunk_text:
            # Create TTS chunk
            chunk = TTSChunk(
                text=chunk_text,
                chunk_id=self.chunk_counter,
                is_final=False
            )
            
            # Send to TTS queue
            self.tts_handler.queue_tts_chunk(chunk)
            
            # Update state
            self.chunk_counter += 1
            self.last_chunk_time = time.time()
            
            print(f"📤 Streaming TTS: Chunk {chunk.chunk_id} queued: '{chunk_text[:30]}...'")
    
    def _extract_chunk_text(self) -> str:
        """Extract the best chunk of text from buffer."""
        if not self.buffer.strip():
            return ""
        
        # Try to break at sentence boundary
        for ending in ['.', '!', '?']:
            if ending in self.buffer:
                pos = self.buffer.rfind(ending)
                if pos > 0:
                    chunk = self.buffer[:pos + 1].strip()
                    self.buffer = self.buffer[pos + 1:].strip()
                    self.word_count = len(self.buffer.split()) if self.buffer else 0
                    return chunk
        
        # Try to break at pause marker
        for marker in [',', ';', ':']:
            marker_with_space = f"{marker} "
            if marker_with_space in self.buffer:
                pos = self.buffer.rfind(marker_with_space)
                if pos > 0:
                    chunk = self.buffer[:pos + 1].strip()
                    self.buffer = self.buffer[pos + 1:].strip()
                    self.word_count = len(self.buffer.split()) if self.buffer else 0
                    return chunk
        
        # Break at word boundary if too long
        words = self.buffer.split()
        if len(words) >= self.max_chunk_length:
            chunk_words = words[:self.max_chunk_length]
            remaining_words = words[self.max_chunk_length:]
            
            chunk = " ".join(chunk_words)
            self.buffer = " ".join(remaining_words)
            self.word_count = len(remaining_words)
            return chunk
        
        # Take minimum chunk if we've waited long enough
        if len(words) >= self.min_chunk_length:
            chunk_words = words[:self.min_chunk_length]
            remaining_words = words[self.min_chunk_length:]
            
            chunk = " ".join(chunk_words)
            self.buffer = " ".join(remaining_words)
            self.word_count = len(remaining_words)
            return chunk
        
        return ""
    
    def finalize(self):
        """Finalize the streaming response and send any remaining text."""
        if self.finalized:
            return
        
        self.finalized = True
        
        # Send any remaining text
        if self.buffer.strip():
            final_chunk = TTSChunk(
                text=self.buffer.strip(),
                chunk_id=self.chunk_counter,
                is_final=True
            )
            
            self.tts_handler.queue_tts_chunk(final_chunk)
            print(f"📤 Streaming TTS: Final chunk {final_chunk.chunk_id} queued: '{self.buffer[:30]}...'")
        
        self.buffer = ""
        self.word_count = 0

# Test function
def test_streaming_tts():
    """Test the streaming TTS handler."""
    print("🧪 Testing Streaming TTS Handler")
    print("=" * 50)
    
    # Mock TTS manager
    class MockTTSManager:
        def speak_text(self, text):
            print(f"🔊 SPEAKING: '{text}'")
            time.sleep(0.1)  # Simulate TTS processing
    
    # Create handler
    tts_manager = MockTTSManager()
    handler = StreamingTTSHandler(tts_manager, min_chunk_length=2, max_chunk_length=8)
    
    # Start streaming response
    stream = handler.start_streaming_response("test_001")
    
    # Simulate streaming text input
    test_response = "Hi there! How can I help you today? I'm here to assist with any questions you might have."
    
    print(f"\n📝 Simulating streaming response: '{test_response}'")
    print("-" * 50)
    
    # Add text word by word with delays
    words = test_response.split()
    for i, word in enumerate(words):
        stream.add_text(word + " ")
        time.sleep(0.2)  # Simulate streaming delay
        
        if i == 2:  # After "Hi there!"
            print("   ⏱️ Should start speaking now...")
    
    # Finalize
    stream.finalize()
    handler.complete_streaming_response("test_001")
    
    # Wait a bit for processing
    time.sleep(2)
    
    # Shutdown
    handler.shutdown()
    
    print("\n✅ Streaming TTS test completed!")

if __name__ == "__main__":
    test_streaming_tts()
