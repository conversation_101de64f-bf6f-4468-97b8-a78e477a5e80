#!/usr/bin/env python3
"""
Multi-Task Assistant for Adrina AI v2.0
Handles query classification and response enhancement using Mistral-7B model
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Import configuration
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.multi_task_config import MultiTaskConfig, DEFAULT_CONFIG

# Import universal logger, query identifier, and command processor
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
from universal_logging.universal_logger import ComponentType, get_logger, initialize_logger
from chatbot.query_identifier import QueryIdentifier, QueryType, ProcessingPath, MemoryAction
from chatbot.command_processor import MultiLayerCommandProcessor, CommandType as CmdType, TaskPriority

try:
    from llama_cpp import <PERSON>lama
except ImportError:
    print("❌ llama-cpp-python not found. Install with: pip install llama-cpp-python")
    Llama = None

from dataclasses import dataclass

@dataclass
class QueryClassification:
    """Result of query classification."""
    query_type: str  # "FAST" or "MEMORY"
    confidence: float
    reasoning: str
    processing_time: float

class MultiTaskAssistant:
    """
    Multi-task assistant that handles query classification and response enhancement.
    Uses Mistral-7B model for intelligent FAST vs MEMORY routing.
    """
    
    def __init__(self, config: Optional[MultiTaskConfig] = None):
        """Initialize the multi-task assistant."""
        self.config = config or DEFAULT_CONFIG
        self.model = None
        self.is_loaded = False
        self.stats = {
            "total_classifications": 0,
            "fast_queries": 0,
            "memory_queries": 0,
            "avg_classification_time": 0.0,
            "classification_history": []
        }
        
        # Set up logging
        self.logger = get_logger()

        # Initialize query identifier and command processor
        self.query_identifier = QueryIdentifier()
        self.command_processor = MultiLayerCommandProcessor()

        # Load model on initialization
        self._load_model()
    
    def _load_model(self) -> bool:
        """Load the Mistral model for classification."""
        if Llama is None:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, Exception("llama-cpp-python not available"), "model loading")
            return False
        
        model_path = Path(self.config.model_path)
        if not model_path.exists():
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, FileNotFoundError(f"Model not found: {model_path}"), "model loading")
            return False
        
        try:
            self.logger.log_system_event(f"Loading Mistral model: {model_path}")
            start_time = time.time()

            self.model = Llama(
                model_path=str(model_path),
                n_ctx=self.config.n_ctx,
                n_threads=self.config.n_threads,
                n_gpu_layers=self.config.n_gpu_layers,
                verbose=self.config.verbose
            )

            load_time = time.time() - start_time
            self.is_loaded = True
            self.logger.log_system_event(f"✅ Mistral model loaded in {load_time:.2f}s", {"load_time": load_time})
            return True

        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "model loading")
            return False
    
    def classify_query(self, query: str, query_id: str = None) -> QueryClassification:
        """
        Classify a query as FAST or MEMORY type.

        Args:
            query: User query to classify
            query_id: Optional query ID for logging

        Returns:
            QueryClassification with type, confidence, and reasoning
        """
        if not self.is_loaded:
            return QueryClassification("FAST", 0.0, "Model not loaded", 0.0)
        
        start_time = time.time()
        
        # Create classification prompt
        prompt = self._create_classification_prompt(query)
        
        try:
            response = self.model(
                prompt,
                max_tokens=self.config.classification_max_tokens,
                temperature=self.config.classification_temperature,
                stop=["</s>", "[INST]", "\n"]
            )
            
            processing_time = time.time() - start_time
            response_text = response['choices'][0]['text'].strip().upper()
            
            # Extract classification
            query_type, confidence = self._parse_classification_response(response_text)
            
            # Update statistics
            self._update_stats(query_type, processing_time)
            
            classification = QueryClassification(
                query_type=query_type,
                confidence=confidence,
                reasoning=response_text,
                processing_time=processing_time
            )
            
            self.logger.log_classification_result(query_id or "unknown", query_type, confidence, processing_time)
            return classification

        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "query classification", query_id or "unknown")
            return QueryClassification("FAST", 0.0, f"Error: {e}", time.time() - start_time)
    
    def _create_classification_prompt(self, query: str) -> str:
        """Create the classification prompt for Mistral."""
        return self.config.classification_prompt.format(query=query)
    
    def _parse_classification_response(self, response: str) -> Tuple[str, float]:
        """Parse the model's classification response."""
        response = response.strip().upper()
        
        # Look for FAST or MEMORY in response
        if "FAST" in response:
            return "FAST", 0.9
        elif "MEMORY" in response:
            return "MEMORY", 0.9
        else:
            # Default to FAST if unclear
            self.logger.log_warning(ComponentType.TASK_CLASSIFIER, f"Unclear classification response: {response}")
            return "FAST", 0.5

    def identify_query_advanced(self, query: str, query_id: str = None) -> dict:
        """
        Advanced query identification using the QueryIdentifier system.

        Args:
            query: User query string
            query_id: Optional query ID for logging

        Returns:
            Dictionary with detailed query analysis
        """
        try:
            # Use the advanced query identifier
            classification = self.query_identifier.identify_query(query)

            # Convert to our system's format
            query_type = "FAST"
            if classification.processing_path == ProcessingPath.MEMORY_PATH:
                query_type = "MEMORY"
            elif classification.processing_path == ProcessingPath.REALTIME_PATH:
                query_type = "REALTIME"
            elif classification.processing_path == ProcessingPath.SYSTEM_PATH:
                query_type = "SYSTEM"

            result = {
                "query_type": query_type,
                "confidence": classification.confidence,
                "reasoning": classification.reasoning,
                "advanced_classification": {
                    "query_type": classification.query_type.value,
                    "processing_path": classification.processing_path.value,
                    "memory_action": classification.memory_action.value,
                    "context_keywords": classification.context_keywords,
                    "temporal_indicators": classification.temporal_indicators,
                    "task_indicators": classification.task_indicators,
                    "memory_storage_location": classification.memory_storage_location,
                    "priority_level": classification.priority_level
                }
            }

            # Log the advanced classification
            self.logger.log_system_event(
                f"Advanced query classification: {classification.query_type.value} -> {query_type}",
                result["advanced_classification"],
                query_id=query_id
            )

            return result

        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "advanced query identification", query_id)
            # Fallback to simple classification
            simple_type, confidence = self.classify_query(query, query_id)
            return {
                "query_type": simple_type,
                "confidence": confidence,
                "reasoning": f"Fallback classification: {simple_type}",
                "advanced_classification": None
            }

    def understand_command(self, user_command: str, query_id: str = None) -> dict:
        """
        Advanced command understanding using the Multi-Layer Command Processor.

        Args:
            user_command: User's command string
            query_id: Optional query ID for logging

        Returns:
            Dictionary with command analysis and task plan
        """
        try:
            # Use the command processor for detailed analysis
            command_analysis = self.command_processor.process_command(user_command)

            # Convert to our system's format
            result = {
                "command_id": command_analysis.command_id,
                "command_understood": True,
                "intent": {
                    "command_type": command_analysis.intent.command_type.value,
                    "action_type": command_analysis.intent.action_type.value,
                    "priority": command_analysis.intent.priority.value,
                    "priority_numeric": command_analysis.intent.priority.value,
                    "confidence": command_analysis.intent.confidence,
                    "reasoning": command_analysis.intent.reasoning
                },
                "targets": command_analysis.intent.target_objects,
                "context_scope": command_analysis.intent.context_scope,
                "urgency_indicators": command_analysis.intent.urgency_indicators,
                "task_plan": None,
                "immediate_action": command_analysis.immediate_action,
                "requires_confirmation": command_analysis.requires_confirmation
            }

            # Include task plan if available
            if command_analysis.task_plan:
                result["task_plan"] = {
                    "task_id": command_analysis.task_plan.task_id,
                    "primary_objective": command_analysis.task_plan.primary_objective,
                    "total_steps": command_analysis.task_plan.total_steps,
                    "estimated_duration": command_analysis.task_plan.estimated_duration,
                    "complexity_level": command_analysis.task_plan.complexity_level,
                    "steps": [
                        {
                            "step_number": step.step_number,
                            "description": step.description,
                            "action_type": step.action_type.value,
                            "estimated_effort": step.estimated_effort,
                            "target_files": step.target_files,
                            "success_criteria": step.success_criteria
                        }
                        for step in command_analysis.task_plan.steps
                    ],
                    "required_tools": command_analysis.task_plan.required_tools,
                    "risk_factors": command_analysis.task_plan.risk_factors
                }

            # Log the command understanding
            self.logger.log_system_event(
                f"Command understood: {command_analysis.intent.command_type.value}",
                {
                    "command_id": command_analysis.command_id,
                    "action_type": command_analysis.intent.action_type.value,
                    "priority": command_analysis.intent.priority.value,
                    "confidence": command_analysis.intent.confidence,
                    "has_task_plan": command_analysis.task_plan is not None
                },
                query_id=query_id
            )

            return result

        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "command understanding", query_id)
            return {
                "command_id": f"cmd_error_{int(time.time() * 1000) % 10000:04d}",
                "command_understood": False,
                "error": str(e),
                "fallback_suggestion": "Try rephrasing your command or use simpler language"
            }
    
    def _update_stats(self, query_type: str, processing_time: float):
        """Update classification statistics."""
        self.stats["total_classifications"] += 1
        
        if query_type == "FAST":
            self.stats["fast_queries"] += 1
        else:
            self.stats["memory_queries"] += 1
        
        # Update average processing time
        total_time = (self.stats["avg_classification_time"] * 
                     (self.stats["total_classifications"] - 1) + processing_time)
        self.stats["avg_classification_time"] = total_time / self.stats["total_classifications"]
        
        # Keep recent history
        self.stats["classification_history"].append({
            "type": query_type,
            "time": processing_time,
            "timestamp": time.time()
        })
        
        # Keep only last 100 classifications
        if len(self.stats["classification_history"]) > 100:
            self.stats["classification_history"] = self.stats["classification_history"][-100:]
    
    def enhance_response(self, query: str, context: str, base_response: str) -> str:
        """
        Enhance a response using context and the assistant model.
        
        Args:
            query: Original user query
            context: Retrieved memory context
            base_response: Base response from main model
            
        Returns:
            Enhanced response incorporating context
        """
        if not self.is_loaded:
            return base_response
        
        enhancement_prompt = self.config.enhancement_prompt.format(
            query=query,
            context=context,
            base_response=base_response
        )
        
        try:
            response = self.model(
                enhancement_prompt,
                max_tokens=self.config.enhancement_max_tokens,
                temperature=self.config.enhancement_temperature,
                stop=["</s>", "[INST]"]
            )
            
            enhanced = response['choices'][0]['text'].strip()
            return enhanced if enhanced else base_response
            
        except Exception as e:
            self.logger.log_error(ComponentType.TASK_CLASSIFIER, e, "response enhancement")
            return base_response
    
    def get_stats(self) -> Dict:
        """Get classification statistics."""
        total = self.stats["total_classifications"]
        if total == 0:
            return self.stats
        
        fast_percentage = (self.stats["fast_queries"] / total) * 100
        memory_percentage = (self.stats["memory_queries"] / total) * 100
        
        return {
            **self.stats,
            "fast_percentage": fast_percentage,
            "memory_percentage": memory_percentage,
            "model_loaded": self.is_loaded,
            "model_path": self.config.model_path
        }
    
    def reset_stats(self):
        """Reset classification statistics."""
        self.stats = {
            "total_classifications": 0,
            "fast_queries": 0,
            "memory_queries": 0,
            "avg_classification_time": 0.0,
            "classification_history": []
        }
        self.logger.log_system_event("Statistics reset")
    
    def test_classification(self) -> Dict:
        """Test the classification system with sample queries."""
        test_cases = [
            {"query": "What is Python?", "expected": "FAST"},
            {"query": "What did we discuss yesterday?", "expected": "MEMORY"},
            {"query": "How do I create a function?", "expected": "FAST"},
            {"query": "Remember my preference for detailed explanations", "expected": "MEMORY"},
            {"query": "What is 2 + 2?", "expected": "FAST"},
            {"query": "What was my pet's name that I mentioned earlier?", "expected": "MEMORY"},
        ]
        
        results = []
        correct = 0
        
        for test_case in test_cases:
            classification = self.classify_query(test_case["query"])
            is_correct = classification.query_type == test_case["expected"]
            
            if is_correct:
                correct += 1
            
            results.append({
                "query": test_case["query"],
                "expected": test_case["expected"],
                "predicted": classification.query_type,
                "correct": is_correct,
                "confidence": classification.confidence,
                "time": classification.processing_time
            })
        
        accuracy = (correct / len(test_cases)) * 100
        
        return {
            "accuracy": accuracy,
            "correct": correct,
            "total": len(test_cases),
            "results": results,
            "avg_time": sum(r["time"] for r in results) / len(results)
        }
    
    def __del__(self):
        """Cleanup when assistant is destroyed."""
        if hasattr(self, 'model') and self.model:
            del self.model

# Example usage and testing
if __name__ == "__main__":
    # Set up logging - already imported above
    initialize_logger("INFO")
    
    print("🤖 Multi-Task Assistant Test")
    print("=" * 50)
    
    # Initialize assistant
    assistant = MultiTaskAssistant()
    
    if not assistant.is_loaded:
        print("❌ Failed to load model")
        exit(1)
    
    # Test classification
    print("\n🧪 Running classification tests...")
    test_results = assistant.test_classification()
    
    print(f"\n📊 Test Results:")
    print(f"Accuracy: {test_results['accuracy']:.1f}%")
    print(f"Correct: {test_results['correct']}/{test_results['total']}")
    print(f"Average Time: {test_results['avg_time']:.3f}s")
    
    # Show individual results
    print(f"\n📝 Individual Results:")
    for result in test_results['results']:
        status = "✅" if result['correct'] else "❌"
        print(f"{status} {result['query'][:40]:<40} | Expected: {result['expected']:<6} | Got: {result['predicted']:<6} | {result['time']:.3f}s")
    
    # Show statistics
    print(f"\n📈 Statistics:")
    stats = assistant.get_stats()
    print(f"Total Classifications: {stats['total_classifications']}")
    print(f"Fast Queries: {stats['fast_queries']} ({stats.get('fast_percentage', 0):.1f}%)")
    print(f"Memory Queries: {stats['memory_queries']} ({stats.get('memory_percentage', 0):.1f}%)")
    print(f"Average Time: {stats['avg_classification_time']:.3f}s")
    
    print("\n✅ Multi-Task Assistant ready for integration!")
