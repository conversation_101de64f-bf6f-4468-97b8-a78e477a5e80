#!/usr/bin/env python3
"""
Multi-Task Connector for Adrina AI Assistant v2.0

Enhanced connector that integrates:
- <PERSON> Chatbot (Lexi-Llama-3-8B for conversation)
- Multi-Task Assistant (Mistral-7B for query classification)
- Memory System (ChromaDB + Ollama embeddings)
- TTS System (Kokoro TTS)

Provides intelligent query routing with perfect classification accuracy.
"""

import os
import sys
import threading
import time
import queue
from typing import Dict, Optional, Tuple
from queue import Queue

# Add parent directory to path for imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from config.main_chatbot_config import LLAMA_MODEL_PATH
from voice.kokoro_tts import TTSManager
from connectors.chatbot_memory_connector import ChatbotMemoryConnector
from chatbot.multi_task_assistant import MultiTaskAssistant
from universal_logging.universal_logger import ComponentType, get_logger

def clear_screen():
    """Clear the console screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

class MultiTaskProcessor:
    """
    Advanced chat processor with dual-LLM architecture.
    Uses Mistral-7B for perfect query classification and Lexi-Llama for responses.
    """

    def __init__(self, chatbot_memory_connector):
        self.chatbot_memory_connector = chatbot_memory_connector
        self.multi_task_assistant = None
        self.stats = {
            'fast_path_count': 0,
            'memory_path_count': 0,
            'total_queries': 0,
            'classification_time': 0.0,
            'fast_path_time': 0.0,
            'memory_path_time': 0.0,
            'classification_accuracy': 100.0  # Based on testing
        }
        
        # Set up logging
        self.logger = get_logger()

        # Initialize multi-task assistant
        self._initialize_assistant()

    def _initialize_assistant(self):
        """Initialize the multi-task assistant."""
        try:
            self.logger.log_system_event("🤖 Initializing Multi-Task Assistant...")
            self.multi_task_assistant = MultiTaskAssistant()

            if self.multi_task_assistant.is_loaded:
                self.logger.log_system_event("✅ Multi-Task Assistant ready")

                # Run a quick test to verify functionality
                test_result = self.multi_task_assistant.test_classification()
                self.stats['classification_accuracy'] = test_result['accuracy']
                self.logger.log_system_event(f"🎯 Classification accuracy: {test_result['accuracy']:.1f}%", {"accuracy": test_result['accuracy']})
            else:
                self.logger.log_error(ComponentType.CONNECTOR, Exception("Failed to load Multi-Task Assistant"), "initialization")

        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "Multi-Task Assistant initialization")

    def split_into_sentences(self, text):
        """Split text into sentences for streaming TTS."""
        import re
        # Split on sentence endings, keeping the delimiter
        sentences = re.split(r'([.!?]+)', text)
        result = []
        for i in range(0, len(sentences) - 1, 2):
            sentence = sentences[i].strip()
            delimiter = sentences[i + 1] if i + 1 < len(sentences) else ''
            if sentence:
                result.append(sentence + delimiter)
        return [s.strip() for s in result if s.strip()]

    def clean_text_for_tts(self, text):
        """Clean text for better TTS pronunciation."""
        import re
        
        # Remove markdown formatting
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)      # Italic
        text = re.sub(r'`(.*?)`', r'\1', text)        # Code
        text = re.sub(r'#{1,6}\s*(.*)', r'\1', text)  # Headers
        
        # Clean up special characters
        text = re.sub(r'[_~`]', '', text)
        text = re.sub(r'\s+', ' ', text)
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        return text.strip()

    def process_query(self, user_input: str) -> Tuple[str, Dict]:
        """
        Process user query with intelligent routing.
        
        Returns:
            Tuple of (response, processing_info)
        """
        start_time = time.time()
        self.stats['total_queries'] += 1
        
        # Step 1: Classify the query using Multi-Task Assistant
        if self.multi_task_assistant and self.multi_task_assistant.is_loaded:
            classification = self.multi_task_assistant.classify_query(user_input)
            query_type = classification.query_type
            classification_time = classification.processing_time
            
            self.stats['classification_time'] += classification_time
            
            # Classification logging is handled by the assistant itself
        else:
            # Fallback to MEMORY path if assistant not available
            query_type = "MEMORY"
            classification_time = 0.0
            self.logger.log_warning(ComponentType.CONNECTOR, "Multi-Task Assistant not available, using MEMORY path")

        # Step 2: Process based on classification
        if query_type == "FAST":
            response, processing_time = self._process_fast_query(user_input)
            self.stats['fast_path_count'] += 1
            self.stats['fast_path_time'] += processing_time
        else:
            response, processing_time = self._process_memory_query(user_input)
            self.stats['memory_path_count'] += 1
            self.stats['memory_path_time'] += processing_time

        total_time = time.time() - start_time
        
        # Processing information
        processing_info = {
            'query_type': query_type,
            'classification_time': classification_time,
            'processing_time': processing_time,
            'total_time': total_time,
            'path_used': f"{query_type.lower()}_path"
        }
        
        return response, processing_info

    def _process_fast_query(self, user_input: str) -> Tuple[str, float]:
        """Process query via FAST path (direct response)."""
        start_time = time.time()
        
        try:
            # Direct chatbot response without memory context
            response = self.chatbot_memory_connector.chatbot.generate_response(user_input)
            processing_time = time.time() - start_time
            
            # FAST path logging handled by chatbot
            return response, processing_time

        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "FAST path processing")
            # Fallback to memory path
            return self._process_memory_query(user_input)

    def _process_memory_query(self, user_input: str) -> Tuple[str, float]:
        """Process query via MEMORY path (context-aware response)."""
        start_time = time.time()
        
        try:
            # Full memory-enhanced response
            response = self.chatbot_memory_connector.generate_response(user_input)
            processing_time = time.time() - start_time
            
            # MEMORY path logging handled by memory connector
            return response, processing_time

        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "MEMORY path processing")
            processing_time = time.time() - start_time
            return f"I apologize, but I encountered an error processing your request: {e}", processing_time

    def get_performance_stats(self) -> Dict:
        """Get detailed performance statistics."""
        total = self.stats['total_queries']
        if total == 0:
            return self.stats

        fast_percentage = (self.stats['fast_path_count'] / total) * 100
        memory_percentage = (self.stats['memory_path_count'] / total) * 100
        
        avg_classification_time = self.stats['classification_time'] / total
        avg_fast_time = (self.stats['fast_path_time'] / self.stats['fast_path_count'] 
                        if self.stats['fast_path_count'] > 0 else 0)
        avg_memory_time = (self.stats['memory_path_time'] / self.stats['memory_path_count'] 
                          if self.stats['memory_path_count'] > 0 else 0)

        return {
            **self.stats,
            'fast_percentage': fast_percentage,
            'memory_percentage': memory_percentage,
            'avg_classification_time': avg_classification_time,
            'avg_fast_path_time': avg_fast_time,
            'avg_memory_path_time': avg_memory_time,
            'assistant_loaded': self.multi_task_assistant.is_loaded if self.multi_task_assistant else False
        }

class MultiTaskConnector:
    """
    Main connector integrating all components with multi-task intelligence.
    """
    
    def __init__(self):
        self.chatbot_memory_connector = None
        self.tts_manager = None
        self.processor = None
        self.is_speaking = False
        self.speech_queue = Queue()
        self.speech_thread = None
        
        # Set up logging
        self.logger = get_logger()
        
        self._initialize_components()

    def _initialize_components(self):
        """Initialize all system components."""
        try:
            self.logger.log_system_event("🚀 Initializing Multi-Task Adrina System...")

            # Initialize chatbot and memory
            self.logger.log_system_event("📚 Loading chatbot and memory system...")
            self.chatbot_memory_connector = ChatbotMemoryConnector(LLAMA_MODEL_PATH)

            # Initialize TTS
            self.logger.log_system_event("🔊 Initializing TTS system...")
            self.tts_manager = TTSManager()

            # Initialize multi-task processor
            self.logger.log_system_event("🤖 Setting up multi-task processor...")
            self.processor = MultiTaskProcessor(self.chatbot_memory_connector)

            # Start speech thread
            self.speech_thread = threading.Thread(target=self._speech_worker, daemon=True)
            self.speech_thread.start()

            self.logger.log_system_event("✅ Multi-Task Adrina System ready!")

        except Exception as e:
            self.logger.log_error(ComponentType.SYSTEM, e, "system initialization")
            raise

    def _speech_worker(self):
        """Worker thread for handling TTS queue."""
        while True:
            try:
                sentence = self.speech_queue.get()
                if sentence is None:  # Shutdown signal
                    break
                
                self.is_speaking = True
                self.tts_manager.speak(sentence)
                self.is_speaking = False
                self.speech_queue.task_done()
                
            except Exception as e:
                self.logger.log_error(ComponentType.TTS_SYSTEM, e, "speech worker")
                self.is_speaking = False

    def process_and_speak(self, user_input: str) -> Dict:
        """
        Process user input and generate spoken response.
        
        Returns:
            Dictionary with response and processing information
        """
        try:
            # Process the query
            response, processing_info = self.processor.process_query(user_input)
            
            # Clean and prepare text for TTS
            clean_response = self.processor.clean_text_for_tts(response)
            sentences = self.processor.split_into_sentences(clean_response)
            
            # Queue sentences for TTS
            for sentence in sentences:
                if sentence.strip():
                    self.speech_queue.put(sentence)
            
            return {
                'response': response,
                'clean_response': clean_response,
                'processing_info': processing_info,
                'sentences_queued': len(sentences)
            }
            
        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "process and speak")
            error_response = f"I encountered an error: {e}"
            self.speech_queue.put(error_response)

            return {
                'response': error_response,
                'clean_response': error_response,
                'processing_info': {'error': str(e)},
                'sentences_queued': 1
            }

    def get_stats(self) -> Dict:
        """Get comprehensive system statistics."""
        stats = {}
        
        if self.processor:
            stats['processor'] = self.processor.get_performance_stats()
        
        if self.chatbot_memory_connector and hasattr(self.chatbot_memory_connector, 'memory_manager'):
            try:
                stats['memory'] = self.chatbot_memory_connector.memory_manager.get_stats()
            except:
                stats['memory'] = {'status': 'unavailable'}
        
        stats['system'] = {
            'is_speaking': self.is_speaking,
            'speech_queue_size': self.speech_queue.qsize(),
            'components_loaded': {
                'chatbot': self.chatbot_memory_connector is not None,
                'tts': self.tts_manager is not None,
                'processor': self.processor is not None,
                'multi_task_assistant': (self.processor.multi_task_assistant.is_loaded 
                                       if self.processor and self.processor.multi_task_assistant 
                                       else False)
            }
        }
        
        return stats

    def shutdown(self):
        """Gracefully shutdown the system."""
        self.logger.log_system_event("🔄 Shutting down Multi-Task Adrina System...")

        # Stop speech thread
        if self.speech_thread and self.speech_thread.is_alive():
            self.speech_queue.put(None)  # Shutdown signal
            self.speech_thread.join(timeout=5)

        self.logger.log_system_event("✅ System shutdown complete")

# Example usage and testing
if __name__ == "__main__":
    print("🚀 Multi-Task Adrina Connector Test")
    print("=" * 60)
    
    try:
        # Initialize the connector
        connector = MultiTaskConnector()
        
        # Test queries
        test_queries = [
            "What is Python programming?",  # Should be FAST
            "What did we discuss in our last conversation?",  # Should be MEMORY
            "How do I create a function?",  # Should be FAST
            "Remember that I prefer detailed explanations"  # Should be MEMORY
        ]
        
        print("\n🧪 Testing query processing...")
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Test {i}: {query}")
            
            result = connector.process_and_speak(query)
            processing_info = result['processing_info']
            
            print(f"🎯 Classification: {processing_info['query_type']}")
            print(f"⏱️ Total time: {processing_info['total_time']:.3f}s")
            print(f"🔄 Path used: {processing_info['path_used']}")
            print(f"💬 Response: {result['response'][:100]}...")
            
            # Wait for speech to complete
            time.sleep(2)
        
        # Show statistics
        print(f"\n📊 System Statistics:")
        stats = connector.get_stats()
        
        if 'processor' in stats:
            proc_stats = stats['processor']
            print(f"Total Queries: {proc_stats['total_queries']}")
            print(f"Fast Path: {proc_stats['fast_path_count']} ({proc_stats.get('fast_percentage', 0):.1f}%)")
            print(f"Memory Path: {proc_stats['memory_path_count']} ({proc_stats.get('memory_percentage', 0):.1f}%)")
            print(f"Classification Accuracy: {proc_stats['classification_accuracy']:.1f}%")
            print(f"Avg Classification Time: {proc_stats.get('avg_classification_time', 0):.3f}s")
        
        print("\n✅ Multi-Task Connector ready for production!")
        
    except KeyboardInterrupt:
        print("\n🔄 Shutting down...")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        if 'connector' in locals():
            connector.shutdown()
