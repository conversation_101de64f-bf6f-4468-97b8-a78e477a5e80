#!/usr/bin/env python3
"""
Multi-Task Main Connector for Adrina AI Assistant v2.0

Main application logic for the multi-task system with clean architecture.
Handles initialization, conversation loop, and command processing.
"""

import os
import sys
import time
import signal

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from connectors.simple_smart_connector import SimpleSmartConnector
from universal_logging.universal_logger import initialize_logger, get_logger, ComponentType
from ui.display import clear_screen, print_banner, print_help, format_stats, format_memory_stats
from ui.commands import run_classification_test
from ui.error_notifications import check_for_errors_during_conversation, display_error_summary

class MultiTaskMainConnector:
    """
    Main application connector for the multi-task Adrina system.
    Provides clean separation between UI and core functionality.
    """
    
    def __init__(self):
        self.connector = None
        self.logger = None
        self.conversation_count = 0
    
    def initialize(self):
        """Initialize the multi-task system."""
        # Initialize logging first
        self.logger = initialize_logger("INFO", enable_console_logging=True)
        
        # Clear screen and show banner
        clear_screen()
        print_banner()
        
        # Initialize the system
        print("🚀 Initializing Multi-Task Adrina System...")
        print("   This may take a moment to load all models...")
        self.logger.log_system_event("Starting Multi-Task Adrina System initialization")
        
        start_time = time.time()
        self.connector = SimpleSmartConnector()
        init_time = time.time() - start_time
        
        print(f"✅ System initialized in {init_time:.2f} seconds!")
        print("\n💡 Type 'help' for commands or just start chatting!")
        print("🎯 Adrina will automatically route your queries for optimal performance.")
        self.logger.log_system_event(f"System initialization completed in {init_time:.2f}s", {"init_time": init_time})
    
    def handle_command(self, user_input):
        """Handle special commands. Returns True if command was handled."""
        command = user_input.lower()
        
        if command in ['quit', 'exit', 'bye']:
            print("👋 Goodbye! Thanks for chatting with Adrina!")
            return 'quit'
        
        elif command == 'help':
            print_help()
            return True
        
        elif command == 'clear':
            clear_screen()
            print_banner()
            return True
        
        elif command == 'stats':
            stats = self.connector.get_stats()
            print(format_stats(stats))
            return True
        
        elif command == 'memory':
            # Get memory stats from the enhanced connector
            if hasattr(self.connector, 'chatbot_memory_connector') and hasattr(self.connector.chatbot_memory_connector, 'get_memory_stats'):
                memory_stats = self.connector.chatbot_memory_connector.get_memory_stats()
                print(format_memory_stats(memory_stats))
            else:
                print("🧠 Memory system statistics unavailable.")
            return True
        
        elif command == 'test':
            run_classification_test(self.connector)
            return True

        elif command in ['errors', 'error']:
            print(display_error_summary())
            return True

        return False
    
    def process_query(self, user_input):
        """Process a user query and return the response."""
        self.conversation_count += 1
        print(f"\n🤖 Adrina is thinking...")
        
        # Log user query
        query_id = self.logger.log_user_query(user_input)
        
        start_time = time.time()
        result = self.connector.process_and_speak_smart(user_input)
        total_time = time.time() - start_time
        
        # Log system response
        self.logger.log_system_response(result['response'], query_id, total_time)
        
        # Display response info
        processing_info = result['processing_info']
        query_type = processing_info['query_type']
        path_icon = "⚡" if query_type == "FAST" else "🧠"
        
        print(f"\n{path_icon} Adrina ({query_type} path, {processing_info['total_time']:.2f}s): {result['response']}")
        
        # Show processing details (optional)
        if processing_info.get('classification_time', 0) > 0:
            print(f"   🎯 Classification: {processing_info['classification_time']:.3f}s | Processing: {processing_info['processing_time']:.3f}s")
    
    def run_conversation_loop(self):
        """Run the main conversation loop."""
        while True:
            try:
                # Get user input
                user_input = input(f"\n🗣️  You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                command_result = self.handle_command(user_input)
                if command_result == 'quit':
                    break
                elif command_result:
                    continue
                
                # Process the query
                self.process_query(user_input)

                # Check for any errors that occurred during processing
                check_for_errors_during_conversation()

            except KeyboardInterrupt:
                break
            except Exception as e:
                self.logger.log_error(ComponentType.USER_INTERFACE, e, "processing user request")
                print(f"\n❌ Error processing your request: {e}")
                print("Please try again or type 'help' for assistance.")
    
    def shutdown(self):
        """Shutdown the system gracefully."""
        if self.connector:
            self.connector.shutdown()
        if self.logger:
            self.logger.shutdown()

def main():
    """Main application entry point."""
    app = MultiTaskMainConnector()
    
    def signal_handler(sig, frame):
        """Handle Ctrl+C gracefully."""
        print("\n\n🔄 Shutting down Adrina...")
        app.shutdown()
        print("👋 Goodbye!")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Initialize the system
        app.initialize()
        
        # Run the conversation loop
        app.run_conversation_loop()
        
    except Exception as e:
        if app.logger:
            app.logger.log_error(ComponentType.SYSTEM, e, "system initialization")
        print(f"\n❌ Failed to initialize Adrina: {e}")
        print("Please check your configuration and try again.")
        return 1
    
    finally:
        app.shutdown()
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
