#!/usr/bin/env python3
"""
Simple Smart Connector for Adrina AI Assistant v2.0

Simplified version of smart routing for testing:
- Tiny LLM: Ultra-fast query classification
- Main LLM: Primary responses
- Mistral LLM: Deep research (optional)
- Basic TTS: Simple text-to-speech without streaming
"""

import os
import sys
import time
from typing import Dict

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from chatbot.tiny_llm_classifier import TinyLLMClassifier, QueryCategory
from connectors.enhanced_chatbot_memory_connector import EnhancedChatbotMemoryConnector
from config.main_chatbot_config import LLAMA_MODEL_PATH
from universal_logging.universal_logger import ComponentType, get_logger

class SimpleSmartConnector:
    """
    Simplified smart routing connector for testing.
    
    Focus on core functionality:
    1. Tiny LLM classification
    2. Smart routing based on category
    3. Basic response generation
    """
    
    def __init__(self):
        self.logger = get_logger()
        
        # Core components
        self.tiny_classifier = None
        self.chatbot_memory_connector = None
        self.mistral_brain = None
        
        # Statistics
        self.stats = {
            'total_queries': 0,
            'simple_queries': 0,
            'complex_queries': 0,
            'research_queries': 0,
            'avg_classification_time': 0.0,
            'avg_response_time': 0.0
        }
        
        # Initialize system
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize core system components."""
        try:
            self.logger.log_system_event("🚀 Initializing Simple Smart System...")
            
            # Initialize tiny LLM classifier
            self.logger.log_system_event("🧠 Loading tiny LLM classifier...")
            self.tiny_classifier = TinyLLMClassifier()
            
            # Initialize main chatbot with enhanced memory
            self.logger.log_system_event("💬 Loading main chatbot with enhanced memory...")
            self.chatbot_memory_connector = EnhancedChatbotMemoryConnector(LLAMA_MODEL_PATH)
            
            # Try to initialize Mistral (optional)
            self.logger.log_system_event("🧠 Loading Mistral brain (optional)...")
            try:
                from chatbot.multi_task_assistant import MultiTaskAssistant
                self.mistral_brain = MultiTaskAssistant()
                if not self.mistral_brain.is_loaded:
                    self.mistral_brain = None
            except Exception as e:
                self.logger.log_warning(ComponentType.TASK_CLASSIFIER, 
                                      f"Mistral brain unavailable: {e}")
                self.mistral_brain = None
            
            self.logger.log_system_event("✅ Simple Smart System ready!")
            
        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "simple smart system initialization")
            raise
    
    def process_and_speak_smart(self, user_input: str) -> Dict:
        """
        Process user input with smart routing.
        
        Args:
            user_input: User's query
            
        Returns:
            Dictionary with response and processing information
        """
        try:
            start_time = time.time()
            self.stats['total_queries'] += 1
            
            # Step 1: Ultra-fast classification
            self.logger.log_system_event(f"🔍 Classifying: '{user_input[:50]}...'")
            classification_start = time.time()
            
            classification = self.tiny_classifier.classify_query(user_input)
            classification_time = time.time() - classification_start
            
            self.logger.log_system_event(
                f"📊 Result: {classification.category.value} "
                f"(confidence: {classification.confidence:.2f}, "
                f"time: {classification.processing_time:.3f}s)"
            )
            
            # Step 2: Route based on classification
            response_start = time.time()
            
            if classification.category == QueryCategory.SIMPLE:
                response = self._handle_simple_query(user_input)
                self.stats['simple_queries'] += 1
                
            elif classification.category == QueryCategory.COMPLEX:
                response = self._handle_complex_query(user_input)
                self.stats['complex_queries'] += 1
                
            elif classification.category == QueryCategory.RESEARCH:
                response = self._handle_research_query(user_input)
                self.stats['research_queries'] += 1
                
            else:  # UNKNOWN
                response = self._handle_simple_query(user_input)
                self.stats['simple_queries'] += 1
            
            response_time = time.time() - response_start
            total_time = time.time() - start_time
            
            # Update statistics
            self._update_stats(classification_time, total_time)
            
            # Prepare response info
            processing_info = {
                'query_type': classification.category.value,
                'classification_confidence': classification.confidence,
                'classification_time': classification_time,
                'response_time': response_time,
                'total_time': total_time,
                'processing_time': response_time,
                'routing_method': 'simple_smart'
            }
            
            return {
                'response': response,
                'clean_response': response,
                'processing_info': processing_info,
                'classification': classification
            }
            
        except Exception as e:
            self.logger.log_error(ComponentType.CONNECTOR, e, "simple smart routing")
            error_response = f"I encountered an error: {e}"
            
            return {
                'response': error_response,
                'clean_response': error_response,
                'processing_info': {
                    'query_type': 'ERROR',
                    'total_time': 0.0,
                    'classification_time': 0.0,
                    'processing_time': 0.0,
                    'error': str(e)
                },
                'classification': None
            }
    
    def _handle_simple_query(self, user_input: str) -> str:
        """Handle simple queries with main chatbot only."""
        self.logger.log_system_event("⚡ Processing SIMPLE query with main chatbot")
        
        try:
            response = self.chatbot_memory_connector.chatbot.generate_response(user_input)
            return response
        except Exception as e:
            self.logger.log_error(ComponentType.MAIN_CHATBOT, e, "simple query processing")
            return f"Sorry, I had trouble processing that simple request: {e}"
    
    def _handle_complex_query(self, user_input: str) -> str:
        """Handle complex queries with main chatbot + optional Mistral."""
        self.logger.log_system_event("🔧 Processing COMPLEX query with main chatbot")
        
        try:
            # Get main response
            main_response = self.chatbot_memory_connector.chatbot.generate_response(user_input)
            
            # Check if Mistral assistance is needed
            if self.mistral_brain and self._needs_mistral_assistance(user_input, main_response):
                self.logger.log_system_event("🧠 Requesting Mistral assistance")
                
                try:
                    # Get Mistral enhancement
                    mistral_enhancement = self.mistral_brain.enhance_response(user_input, main_response)
                    if mistral_enhancement and mistral_enhancement.strip():
                        return main_response + "\n\n" + mistral_enhancement
                except Exception as e:
                    self.logger.log_warning(ComponentType.TASK_CLASSIFIER, 
                                          f"Mistral assistance failed: {e}")
            
            return main_response
            
        except Exception as e:
            self.logger.log_error(ComponentType.MAIN_CHATBOT, e, "complex query processing")
            return f"Sorry, I had trouble processing that complex request: {e}"
    
    def _handle_research_query(self, user_input: str) -> str:
        """Handle research queries with Mistral brain (if available)."""
        if self.mistral_brain:
            self.logger.log_system_event("🔬 Processing RESEARCH query with Mistral brain")
            
            try:
                research_response = self.mistral_brain.deep_research_response(user_input)
                return research_response
            except Exception as e:
                self.logger.log_warning(ComponentType.TASK_CLASSIFIER, 
                                      f"Mistral research failed: {e}")
        
        # Fallback to main chatbot
        self.logger.log_system_event("📚 Processing RESEARCH query with main chatbot (fallback)")
        return self._handle_complex_query(user_input)
    
    def _needs_mistral_assistance(self, query: str, response: str) -> bool:
        """Determine if a complex query needs Mistral assistance."""
        # Check for technical complexity indicators
        technical_keywords = [
            'authentication', 'security', 'database', 'architecture', 
            'system', 'implementation', 'algorithm', 'framework'
        ]
        
        urgent_indicators = ['urgent:', 'priority:', 'critical:']
        
        # Always assist urgent queries
        if any(indicator in query.lower() for indicator in urgent_indicators):
            return True
        
        # Assist technical queries
        if any(keyword in query.lower() for keyword in technical_keywords):
            return True
        
        # Assist if main response seems short for complex query
        if len(query.split()) > 10 and len(response.split()) < 50:
            return True
        
        return False
    
    def _update_stats(self, classification_time: float, total_time: float):
        """Update processing statistics."""
        total = self.stats['total_queries']
        
        # Update classification time average
        current_class_avg = self.stats['avg_classification_time']
        self.stats['avg_classification_time'] = ((current_class_avg * (total - 1)) + classification_time) / total
        
        # Update response time average
        current_resp_avg = self.stats['avg_response_time']
        self.stats['avg_response_time'] = ((current_resp_avg * (total - 1)) + total_time) / total
    
    def get_stats(self) -> Dict:
        """Get connector statistics."""
        return {
            'total_queries': self.stats['total_queries'],
            'simple_queries': self.stats['simple_queries'],
            'complex_queries': self.stats['complex_queries'],
            'research_queries': self.stats['research_queries'],
            'avg_classification_time': self.stats['avg_classification_time'],
            'avg_response_time': self.stats['avg_response_time'],
            'tiny_classifier_stats': self.tiny_classifier.get_stats() if self.tiny_classifier else {},
            'mistral_available': self.mistral_brain is not None
        }
    
    def shutdown(self):
        """Shutdown the simple smart connector."""
        self.logger.log_system_event("🔄 Shutting down Simple Smart System...")
        self.logger.log_system_event("✅ Simple Smart System shutdown complete")

# Test function
def test_simple_smart():
    """Test the simple smart connector."""
    print("🧪 Testing Simple Smart Connector")
    print("=" * 50)
    
    connector = SimpleSmartConnector()
    
    test_queries = [
        "hi there",
        "what's your name?",
        "create a secure authentication system",
        "explain quantum computing in detail",
        "tell me a joke"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        result = connector.process_and_speak_smart(query)
        
        print(f"📊 Category: {result['processing_info']['query_type']}")
        print(f"⏱️ Time: {result['processing_info']['total_time']:.3f}s")
        print(f"💬 Response: {result['response'][:100]}...")
    
    print("\n📊 Final Statistics:")
    stats = connector.get_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    connector.shutdown()

if __name__ == "__main__":
    test_simple_smart()
