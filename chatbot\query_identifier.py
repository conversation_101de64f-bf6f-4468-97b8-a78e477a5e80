#!/usr/bin/env python3
"""
Query Identifier System for Adrina AI Assistant v2.0

Advanced query classification system that identifies:
- General chat queries
- Real-time queries  
- Task execution commands
- Memory storage requirements
- Context extraction needs
"""

import re
import time
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

class QueryType(Enum):
    """Types of queries the system can handle."""
    GENERAL_CHAT = "general_chat"           # Casual conversation
    REALTIME_QUERY = "realtime_query"       # Time-sensitive information
    TASK_EXECUTION = "task_execution"       # Commands to execute
    MEMORY_RETRIEVAL = "memory_retrieval"   # Accessing stored information
    MEMORY_STORAGE = "memory_storage"       # Storing new information
    SYSTEM_COMMAND = "system_command"       # System control commands

class MemoryAction(Enum):
    """Memory actions to take with queries."""
    STORE_FULL = "store_full"               # Store complete conversation
    STORE_CONTEXT = "store_context"         # Store only important context
    STORE_FACT = "store_fact"               # Store as factual information
    STORE_PREFERENCE = "store_preference"   # Store as user preference
    NO_STORAGE = "no_storage"               # Don't store in memory
    RETRIEVE_ONLY = "retrieve_only"         # Only retrieve, don't store

class ProcessingPath(Enum):
    """Processing paths for different query types."""
    FAST_PATH = "fast"                      # Direct LLM processing
    MEMORY_PATH = "memory"                  # Memory-enhanced processing
    REALTIME_PATH = "realtime"              # Real-time data processing
    SYSTEM_PATH = "system"                  # System command processing

@dataclass
class QueryClassification:
    """Complete query classification result."""
    query_type: QueryType
    processing_path: ProcessingPath
    memory_action: MemoryAction
    confidence: float
    reasoning: str
    context_keywords: List[str]
    temporal_indicators: List[str]
    task_indicators: List[str]
    memory_storage_location: Optional[str] = None
    priority_level: int = 1  # 1=low, 2=medium, 3=high

class QueryIdentifier:
    """
    Advanced query identifier that determines query type, processing path,
    and memory storage requirements.
    """
    
    def __init__(self):
        self.setup_patterns()
    
    def setup_patterns(self):
        """Setup regex patterns for different query types."""
        
        # General chat patterns
        self.general_chat_patterns = [
            r'\b(hello|hi|hey|good morning|good afternoon|good evening)\b',
            r'\b(how are you|what\'s up|how\'s it going)\b',
            r'\b(thank you|thanks|appreciate)\b',
            r'\b(goodbye|bye|see you|talk later)\b',
            r'\b(nice|great|awesome|cool|interesting)\b'
        ]
        
        # Real-time query patterns
        self.realtime_patterns = [
            r'\b(current|now|today|right now|at the moment)\b',
            r'\b(latest|recent|newest|up to date)\b',
            r'\b(what time|current time|what date|today\'s date)\b',
            r'\b(weather|temperature|forecast)\b',
            r'\b(news|breaking|happening now)\b',
            r'\b(stock price|market|exchange rate)\b'
        ]
        
        # Task execution patterns
        self.task_patterns = [
            r'\b(create|make|build|generate|write)\b',
            r'\b(calculate|compute|solve|find)\b',
            r'\b(search|look up|find information)\b',
            r'\b(remind me|set reminder|schedule)\b',
            r'\b(send|email|message|call)\b',
            r'\b(open|start|launch|run)\b'
        ]
        
        # Memory retrieval patterns
        self.memory_retrieval_patterns = [
            r'\b(remember|recall|what did|do you know)\b',
            r'\b(we discussed|we talked about|mentioned before)\b',
            r'\b(my preference|I like|I prefer|I told you)\b',
            r'\b(last time|previously|earlier|before)\b',
            r'\b(what was|who was|when was|where was)\b'
        ]
        
        # Memory storage indicators
        self.memory_storage_patterns = [
            r'\b(remember that|keep in mind|note that)\b',
            r'\b(my name is|I am|I work at|I live in)\b',
            r'\b(I like|I prefer|I enjoy|I hate|I dislike)\b',
            r'\b(important|don\'t forget|make sure)\b',
            r'\b(for future reference|next time)\b'
        ]
        
        # System command patterns
        self.system_patterns = [
            r'\b(stats|statistics|performance|status)\b',
            r'\b(help|commands|what can you do)\b',
            r'\b(clear|reset|restart|shutdown)\b',
            r'\b(test|debug|check|verify)\b',
            r'\b(memory|database|logs|files)\b'
        ]
        
        # Temporal indicators
        self.temporal_indicators = [
            r'\b(today|tomorrow|yesterday|now|currently)\b',
            r'\b(this week|next week|last week)\b',
            r'\b(this month|next month|last month)\b',
            r'\b(morning|afternoon|evening|night)\b',
            r'\b(\d{1,2}:\d{2}|\d{1,2} (am|pm))\b'  # Time formats
        ]
        
        # Context keywords that should be stored
        self.context_keywords = [
            r'\b(project|work|job|career|company)\b',
            r'\b(family|friend|relationship|personal)\b',
            r'\b(hobby|interest|passion|goal)\b',
            r'\b(problem|issue|challenge|solution)\b',
            r'\b(plan|idea|thought|opinion)\b'
        ]
    
    def identify_query(self, query: str, user_context: Dict[str, Any] = None) -> QueryClassification:
        """
        Identify query type and determine processing requirements.
        
        Args:
            query: User query string
            user_context: Optional user context information
            
        Returns:
            QueryClassification with complete analysis
        """
        query_lower = query.lower().strip()
        
        # Initialize classification
        classification = QueryClassification(
            query_type=QueryType.GENERAL_CHAT,
            processing_path=ProcessingPath.FAST_PATH,
            memory_action=MemoryAction.NO_STORAGE,
            confidence=0.0,
            reasoning="",
            context_keywords=[],
            temporal_indicators=[],
            task_indicators=[]
        )
        
        # Analyze query patterns
        scores = self._analyze_patterns(query_lower)
        
        # Determine primary query type
        primary_type, confidence = self._determine_primary_type(scores)
        classification.query_type = primary_type
        classification.confidence = confidence
        
        # Determine processing path
        classification.processing_path = self._determine_processing_path(primary_type, query_lower)
        
        # Determine memory action
        classification.memory_action = self._determine_memory_action(primary_type, query_lower)
        
        # Extract indicators
        classification.context_keywords = self._extract_context_keywords(query_lower)
        classification.temporal_indicators = self._extract_temporal_indicators(query_lower)
        classification.task_indicators = self._extract_task_indicators(query_lower)
        
        # Determine storage location
        classification.memory_storage_location = self._determine_storage_location(classification)
        
        # Set priority level
        classification.priority_level = self._determine_priority(classification)
        
        # Generate reasoning
        classification.reasoning = self._generate_reasoning(classification, scores)
        
        return classification
    
    def _analyze_patterns(self, query: str) -> Dict[QueryType, float]:
        """Analyze query against all patterns and return scores."""
        scores = {query_type: 0.0 for query_type in QueryType}
        
        # General chat analysis
        for pattern in self.general_chat_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                scores[QueryType.GENERAL_CHAT] += 1.0
        
        # Real-time query analysis
        for pattern in self.realtime_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                scores[QueryType.REALTIME_QUERY] += 1.0
        
        # Task execution analysis
        for pattern in self.task_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                scores[QueryType.TASK_EXECUTION] += 1.0
        
        # Memory retrieval analysis
        for pattern in self.memory_retrieval_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                scores[QueryType.MEMORY_RETRIEVAL] += 1.0
        
        # Memory storage analysis
        for pattern in self.memory_storage_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                scores[QueryType.MEMORY_STORAGE] += 1.0
        
        # System command analysis
        for pattern in self.system_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                scores[QueryType.SYSTEM_COMMAND] += 1.0
        
        return scores
    
    def _determine_primary_type(self, scores: Dict[QueryType, float]) -> Tuple[QueryType, float]:
        """Determine the primary query type from scores."""
        if not any(scores.values()):
            return QueryType.GENERAL_CHAT, 0.5
        
        max_score = max(scores.values())
        primary_types = [qtype for qtype, score in scores.items() if score == max_score]
        
        # If tie, use priority order
        priority_order = [
            QueryType.SYSTEM_COMMAND,
            QueryType.TASK_EXECUTION,
            QueryType.REALTIME_QUERY,
            QueryType.MEMORY_RETRIEVAL,
            QueryType.MEMORY_STORAGE,
            QueryType.GENERAL_CHAT
        ]
        
        for qtype in priority_order:
            if qtype in primary_types:
                confidence = min(max_score / len(primary_types), 1.0)
                return qtype, confidence
        
        return QueryType.GENERAL_CHAT, 0.5
    
    def _determine_processing_path(self, query_type: QueryType, query: str) -> ProcessingPath:
        """Determine the appropriate processing path."""
        if query_type == QueryType.SYSTEM_COMMAND:
            return ProcessingPath.SYSTEM_PATH
        elif query_type == QueryType.REALTIME_QUERY:
            return ProcessingPath.REALTIME_PATH
        elif query_type in [QueryType.MEMORY_RETRIEVAL, QueryType.MEMORY_STORAGE]:
            return ProcessingPath.MEMORY_PATH
        elif query_type == QueryType.TASK_EXECUTION:
            # Complex tasks might need memory context
            if any(word in query for word in ['remember', 'context', 'previous', 'before']):
                return ProcessingPath.MEMORY_PATH
            return ProcessingPath.FAST_PATH
        else:
            return ProcessingPath.FAST_PATH
    
    def _determine_memory_action(self, query_type: QueryType, query: str) -> MemoryAction:
        """Determine what memory action to take."""
        if query_type == QueryType.MEMORY_STORAGE:
            if any(word in query for word in ['prefer', 'like', 'enjoy', 'hate']):
                return MemoryAction.STORE_PREFERENCE
            elif any(word in query for word in ['fact', 'information', 'data']):
                return MemoryAction.STORE_FACT
            else:
                return MemoryAction.STORE_CONTEXT
        
        elif query_type == QueryType.MEMORY_RETRIEVAL:
            return MemoryAction.RETRIEVE_ONLY
        
        elif query_type == QueryType.TASK_EXECUTION:
            return MemoryAction.STORE_CONTEXT
        
        elif query_type == QueryType.GENERAL_CHAT:
            # Store meaningful conversations
            if len(query.split()) > 5:  # Longer conversations
                return MemoryAction.STORE_CONTEXT
            return MemoryAction.NO_STORAGE
        
        elif query_type in [QueryType.REALTIME_QUERY, QueryType.SYSTEM_COMMAND]:
            return MemoryAction.NO_STORAGE
        
        return MemoryAction.NO_STORAGE
    
    def _extract_context_keywords(self, query: str) -> List[str]:
        """Extract context keywords from query."""
        keywords = []
        for pattern in self.context_keywords:
            matches = re.findall(pattern, query, re.IGNORECASE)
            keywords.extend(matches)
        return list(set(keywords))
    
    def _extract_temporal_indicators(self, query: str) -> List[str]:
        """Extract temporal indicators from query."""
        indicators = []
        for pattern in self.temporal_indicators:
            matches = re.findall(pattern, query, re.IGNORECASE)
            indicators.extend(matches)
        return list(set(indicators))
    
    def _extract_task_indicators(self, query: str) -> List[str]:
        """Extract task indicators from query."""
        indicators = []
        for pattern in self.task_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            indicators.extend(matches)
        return list(set(indicators))
    
    def _determine_storage_location(self, classification: QueryClassification) -> Optional[str]:
        """Determine where to store the information in memory."""
        if classification.memory_action == MemoryAction.NO_STORAGE:
            return None
        
        if classification.memory_action == MemoryAction.STORE_PREFERENCE:
            return "user_preferences"
        elif classification.memory_action == MemoryAction.STORE_FACT:
            return "factual_knowledge"
        elif classification.memory_action == MemoryAction.STORE_CONTEXT:
            return "conversation_context"
        elif classification.memory_action == MemoryAction.STORE_FULL:
            return "full_conversations"
        
        return "general_memory"
    
    def _determine_priority(self, classification: QueryClassification) -> int:
        """Determine priority level for the query."""
        if classification.query_type == QueryType.SYSTEM_COMMAND:
            return 3  # High priority
        elif classification.query_type == QueryType.REALTIME_QUERY:
            return 2  # Medium priority
        elif classification.query_type == QueryType.TASK_EXECUTION:
            return 2  # Medium priority
        else:
            return 1  # Low priority
    
    def _generate_reasoning(self, classification: QueryClassification, scores: Dict[QueryType, float]) -> str:
        """Generate human-readable reasoning for the classification."""
        reasoning_parts = []
        
        # Primary classification reason
        reasoning_parts.append(f"Classified as {classification.query_type.value} (confidence: {classification.confidence:.2f})")
        
        # Pattern matches
        if scores[classification.query_type] > 0:
            reasoning_parts.append(f"Matched {int(scores[classification.query_type])} relevant patterns")
        
        # Processing path reason
        reasoning_parts.append(f"Routed to {classification.processing_path.value} path")
        
        # Memory action reason
        if classification.memory_action != MemoryAction.NO_STORAGE:
            reasoning_parts.append(f"Memory action: {classification.memory_action.value}")
        
        # Context indicators
        if classification.context_keywords:
            reasoning_parts.append(f"Context keywords: {', '.join(classification.context_keywords[:3])}")
        
        return " | ".join(reasoning_parts)

# Test function
def test_query_identifier():
    """Test the query identifier with various query types."""
    identifier = QueryIdentifier()
    
    test_queries = [
        "Hello, how are you today?",
        "What's the current weather in New York?",
        "Remember that I prefer detailed explanations",
        "What did we discuss about Python yesterday?",
        "Create a simple calculator program",
        "Show me the system statistics",
        "What is machine learning?",
        "My name is John and I work at Google",
        "What time is it right now?",
        "Calculate the square root of 144"
    ]
    
    print("🧪 Testing Query Identifier System")
    print("=" * 60)
    
    for query in test_queries:
        classification = identifier.identify_query(query)
        print(f"\nQuery: '{query}'")
        print(f"Type: {classification.query_type.value}")
        print(f"Path: {classification.processing_path.value}")
        print(f"Memory: {classification.memory_action.value}")
        print(f"Confidence: {classification.confidence:.2f}")
        print(f"Reasoning: {classification.reasoning}")

if __name__ == "__main__":
    test_query_identifier()
