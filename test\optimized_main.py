#!/usr/bin/env python3
"""
Optimized Main - LLM Self-Decision System
Tests the smart pipeline where LLM decides if it needs memory context.

This implements:
1. LLM self-decision for memory usage
2. Fast path for standalone queries
3. Full path for contextual queries
4. Async processing for better performance
"""

import asyncio
import os
import sys
import time
import re
from threading import Thread
from queue import Queue

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chatbot.chatbot import Chatbot
from memory.memory_manager import MemoryManager
from voice.kokoro_tts import TTSManager
from config.main_chatbot_config import LLAMA_MODEL_PATH

class OptimizedChatSystem:
    """Optimized chat system with LLM self-decision for memory usage."""
    
    def __init__(self):
        self.chatbot = None
        self.memory_manager = None
        self.tts_manager = None
        self.stats = {
            'fast_path_count': 0,
            'memory_path_count': 0,
            'total_time_saved': 0
        }
    
    async def initialize(self):
        """Initialize all components."""
        print("🚀 Initializing Optimized Chat System...")
        
        # Initialize chatbot (required)
        print("📱 Loading LLM...")
        start_time = time.time()
        self.chatbot = Chatbot(LLAMA_MODEL_PATH)
        llm_time = time.time() - start_time
        print(f"✅ LLM loaded in {llm_time:.2f}s")
        
        # Initialize memory manager (for complex queries)
        print("🧠 Loading Memory System...")
        start_time = time.time()
        try:
            self.memory_manager = MemoryManager()
            memory_time = time.time() - start_time
            print(f"✅ Memory system loaded in {memory_time:.2f}s")
        except Exception as e:
            print(f"⚠️ Memory system failed to load: {e}")
            self.memory_manager = None
        
        # Initialize TTS
        print("🔊 Loading TTS System...")
        start_time = time.time()
        try:
            self.tts_manager = TTSManager()
            tts_time = time.time() - start_time
            print(f"✅ TTS system loaded in {tts_time:.2f}s")
        except Exception as e:
            print(f"⚠️ TTS system failed to load: {e}")
            self.tts_manager = None
        
        print("✅ Optimized system ready!")
    
    def llm_decide_memory_need(self, user_input):
        """Let LLM decide if it needs memory context."""
        decision_prompt = f"""You are analyzing a conversational query to decide if you need previous conversation history.

Query: "{user_input}"

This is a natural conversation. Do you need previous conversation context to answer this properly?

Answer ONLY "YES" or "NO":
- YES: If the query references past conversations, uses pronouns without clear antecedents, asks about previous topics, or asks "what did I say" type questions
- NO: If the query is a standalone question, greeting, general inquiry, or can be answered without conversation history

Decision:"""

        try:
            # Quick decision with minimal tokens (no temperature parameter)
            decision_response = ""
            for chunk in self.chatbot.generate_response_stream(
                decision_prompt,
                max_tokens=5
            ):
                decision_response += chunk

            needs_memory = "YES" in decision_response.upper()
            decision_type = "MEMORY" if needs_memory else "FAST"
            print(f"🤖 LLM Decision: {decision_type} path for '{user_input[:50]}...'")

            return needs_memory

        except Exception as e:
            print(f"⚠️ Decision error: {e}, defaulting to memory path")
            return True  # Default to safe path
    
    async def retrieve_memory_context(self, user_input):
        """Retrieve relevant memory context."""
        if not self.memory_manager:
            return ""

        try:
            print("🧠 Retrieving memory context...")
            start_time = time.time()

            # Get conversation context
            conversation_context = self.memory_manager.get_conversation_context(user_input)

            # Get relevant memories (facts and preferences)
            relevant_memories = self.memory_manager.retrieve_relevant_memories(
                query_text=user_input,
                memory_types=["fact", "preference"],
                max_results=3
            )

            # Format context
            context_parts = []
            if conversation_context:
                context_parts.append(f"Recent conversation:\n{conversation_context}")

            if relevant_memories:
                memory_texts = [memory["text"] for memory in relevant_memories]
                context_parts.append(f"Relevant information:\n" + "\n".join(memory_texts))

            context = "\n\n".join(context_parts) if context_parts else ""

            memory_time = time.time() - start_time
            print(f"✅ Memory retrieved in {memory_time:.2f}s")

            return context

        except Exception as e:
            print(f"⚠️ Memory retrieval error: {e}")
            return ""

    def clean_response_text(self, response_text):
        """Clean response text to remove unwanted prefixes and suffixes."""
        if not response_text:
            return response_text

        # Remove common unwanted prefixes that might be echoed from the prompt
        unwanted_prefixes = ["Adrina:", "Adrina ", "Assistant:", "Assistant ", "AI:", "Bot:"]

        # Clean the response
        cleaned_text = response_text.strip()

        # Remove unwanted prefixes
        for prefix in unwanted_prefixes:
            if cleaned_text.startswith(prefix):
                cleaned_text = cleaned_text[len(prefix):].strip()

        # Remove unwanted suffixes (like standalone "Adrina" at the end)
        unwanted_suffixes = ["Adrina", "Assistant", "AI", "Bot"]

        for suffix in unwanted_suffixes:
            if cleaned_text.endswith(suffix) and len(cleaned_text) > len(suffix):
                # Only remove if it's a standalone word at the end
                if cleaned_text.endswith(" " + suffix):
                    cleaned_text = cleaned_text[:-len(suffix)-1].strip()
                elif cleaned_text == suffix:
                    cleaned_text = ""

        # Remove self-references in the middle of responses
        # Replace "I'm Adrina" with "I'm an AI assistant"
        import re
        cleaned_text = re.sub(r'\bI\'m Adrina\b', "I'm an AI assistant", cleaned_text, flags=re.IGNORECASE)
        cleaned_text = re.sub(r'\bI am Adrina\b', "I am an AI assistant", cleaned_text, flags=re.IGNORECASE)
        cleaned_text = re.sub(r'\bmy name is Adrina\b', "I'm an AI assistant", cleaned_text, flags=re.IGNORECASE)
        cleaned_text = re.sub(r'\bYou are Adrina\b', "You are an AI assistant", cleaned_text, flags=re.IGNORECASE)

        # Remove validation prompts and meta-commentary
        cleaned_text = re.sub(r'\.\s*Adrina\'s responses are.*?YES or NO:\s*YES\s*YES.*$', '.', cleaned_text, flags=re.IGNORECASE | re.DOTALL)
        cleaned_text = re.sub(r'\.\s*Do you agree with this response\?.*?YES.*$', '.', cleaned_text, flags=re.IGNORECASE | re.DOTALL)
        cleaned_text = re.sub(r'\.\s*In this case.*?YES.*$', '.', cleaned_text, flags=re.IGNORECASE | re.DOTALL)

        # Remove hash-tagged metadata (like #mood#chat#assistant)
        cleaned_text = re.sub(r'\s*#[a-zA-Z]+(?:#[a-zA-Z]+)*\s*', ' ', cleaned_text)

        # Remove standalone "Adrina" at the end, including after newlines
        cleaned_text = re.sub(r'\n\s*Adrina\s*$', '', cleaned_text, flags=re.IGNORECASE)
        cleaned_text = re.sub(r'\s+Adrina\s*$', '', cleaned_text, flags=re.IGNORECASE)

        # Clean up any trailing whitespace or newlines
        cleaned_text = cleaned_text.strip()

        return cleaned_text

    def generate_fast_response(self, user_input):
        """Generate response without memory context (fast path)."""
        print("⚡ Fast path: Direct LLM generation")
        start_time = time.time()
        
        response_text = ""
        for chunk in self.chatbot.generate_response_stream(user_input):
            print(chunk, end="", flush=True)
            response_text += chunk

        # Clean response text to remove unwanted prefixes/suffixes
        response_text = self.clean_response_text(response_text)

        generation_time = time.time() - start_time
        print(f"\n⚡ Fast generation completed in {generation_time:.2f}s")

        self.stats['fast_path_count'] += 1
        return response_text

    def generate_fast_response_streaming(self, user_input, text_queue):
        """Generate response without memory context with streaming to TTS."""
        print("⚡ Fast path: Direct LLM generation with streaming")
        start_time = time.time()

        response_text = ""
        for chunk in self.chatbot.generate_response_stream(user_input):
            print(chunk, end="", flush=True)
            response_text += chunk

            # Send chunk to TTS queue for immediate processing
            text_queue.put(chunk)

        # Clean response text to remove unwanted prefixes/suffixes
        response_text = self.clean_response_text(response_text)

        # Signal end of stream to TTS
        text_queue.put(None)

        generation_time = time.time() - start_time
        print(f"\n⚡ Fast generation completed in {generation_time:.2f}s")

        self.stats['fast_path_count'] += 1
        return response_text

    async def generate_memory_response(self, user_input):
        """Generate response with memory context (full path)."""
        print("🧠 Memory path: LLM + Context generation")
        start_time = time.time()
        
        # Retrieve memory context
        memory_context = await self.retrieve_memory_context(user_input)
        
        # Generate response with context
        if memory_context:
            enhanced_prompt = f"Context from previous conversations:\n{memory_context}\n\nUser: {user_input}\nAssistant:"
        else:
            enhanced_prompt = user_input
        
        response_text = ""
        for chunk in self.chatbot.generate_response_stream(enhanced_prompt):
            print(chunk, end="", flush=True)
            response_text += chunk

        # Clean response text to remove unwanted prefixes/suffixes
        response_text = self.clean_response_text(response_text)

        generation_time = time.time() - start_time
        print(f"\n🧠 Memory generation completed in {generation_time:.2f}s")

        self.stats['memory_path_count'] += 1
        return response_text

    async def generate_memory_response_streaming(self, user_input, text_queue):
        """Generate response with memory context with streaming to TTS."""
        print("🧠 Memory path: LLM + Context generation with streaming")
        start_time = time.time()

        # Retrieve memory context
        memory_context = await self.retrieve_memory_context(user_input)

        # Generate response with context
        if memory_context:
            enhanced_prompt = f"Context from previous conversations:\n{memory_context}\n\nUser: {user_input}\nAssistant:"
        else:
            enhanced_prompt = user_input

        response_text = ""
        for chunk in self.chatbot.generate_response_stream(enhanced_prompt):
            print(chunk, end="", flush=True)
            response_text += chunk

            # Send chunk to TTS queue for immediate processing
            text_queue.put(chunk)

        # Clean response text to remove unwanted prefixes/suffixes
        response_text = self.clean_response_text(response_text)

        # Signal end of stream to TTS
        text_queue.put(None)

        generation_time = time.time() - start_time
        print(f"\n🧠 Memory generation completed in {generation_time:.2f}s")

        self.stats['memory_path_count'] += 1
        return response_text

    def speak_response(self, response_text):
        """Convert response to speech."""
        if not self.tts_manager:
            return

        try:
            print("🔊 Converting to speech...")
            start_time = time.time()

            # Use correct TTS method name
            self.tts_manager.speak(response_text)

            tts_time = time.time() - start_time
            print(f"✅ TTS completed in {tts_time:.2f}s")

        except Exception as e:
            print(f"⚠️ TTS error: {e}")

    def stream_tts_response(self, text_queue):
        """Stream TTS processing as text chunks arrive."""
        sentence_buffer = ""

        while True:
            try:
                # Get text chunk from queue (blocking with timeout)
                chunk = text_queue.get(timeout=0.1)

                if chunk is None:  # End of stream signal
                    # Process any remaining text in buffer
                    if sentence_buffer.strip():
                        # Clean final chunk of hash tags
                        final_chunk = re.sub(r'\s*#[a-zA-Z]+(?:#[a-zA-Z]+)*\s*', ' ', sentence_buffer.strip())
                        if final_chunk.strip():
                            print(f"🎤 TTS: Processing final chunk: '{final_chunk.strip()}'")
                            self.tts_manager.speak(final_chunk.strip())
                    break

                sentence_buffer += chunk

                # Filter out hash-tagged metadata from the buffer
                sentence_buffer = re.sub(r'\s*#[a-zA-Z]+(?:#[a-zA-Z]+)*\s*', ' ', sentence_buffer)

                # Check if we have complete sentences to process
                sentences = self.split_into_sentences(sentence_buffer)

                if len(sentences) > 1:  # We have at least one complete sentence
                    # Process all complete sentences except the last (incomplete) one
                    for sentence in sentences[:-1]:
                        if sentence.strip():
                            print(f"🎤 TTS: Processing chunk: '{sentence.strip()}'")
                            self.tts_manager.speak(sentence.strip())

                    # Keep the last (potentially incomplete) sentence in buffer
                    sentence_buffer = sentences[-1]

                text_queue.task_done()

            except:  # Timeout or queue empty
                continue

    def split_into_sentences(self, text):
        """Split text into sentences for streaming TTS."""
        # Split on sentence endings, keeping the delimiter
        sentences = re.split(r'([.!?]+)', text)

        # Recombine sentences with their punctuation
        result = []
        for i in range(0, len(sentences) - 1, 2):
            if i + 1 < len(sentences):
                sentence = sentences[i] + sentences[i + 1]
                if sentence.strip():
                    result.append(sentence)

        # Add any remaining text
        if len(sentences) % 2 == 1 and sentences[-1].strip():
            result.append(sentences[-1])

        return result if result else [text]

    async def process_query(self, user_input):
        """Main query processing with smart routing."""
        print(f"\n{'='*60}")
        print(f"Processing: {user_input}")
        print(f"{'='*60}")
        
        total_start_time = time.time()
        
        # Step 1: Let LLM decide if it needs memory
        decision_start = time.time()
        needs_memory = self.llm_decide_memory_need(user_input)
        decision_time = time.time() - decision_start
        print(f"🤖 Decision made in {decision_time:.2f}s")
        
        # Step 2: Generate response with streaming TTS
        response_start = time.time()

        # Create queue for streaming text to TTS
        text_queue = Queue()

        # Start TTS streaming in background thread
        tts_thread = Thread(target=self.stream_tts_response, args=(text_queue,))
        tts_thread.daemon = True
        tts_thread.start()

        # Generate response with streaming
        if needs_memory:
            response_text = await self.generate_memory_response_streaming(user_input, text_queue)
        else:
            response_text = self.generate_fast_response_streaming(user_input, text_queue)

        response_time = time.time() - response_start
        print(f"📝 Response generation completed in {response_time:.2f}s")

        # Wait for TTS to complete
        tts_thread.join(timeout=10)  # 10 second timeout
        print(f"🔊 Streaming TTS completed")
        
        # Step 4: Store in memory if available
        if self.memory_manager:
            try:
                self.memory_manager.store_conversation(user_input, response_text)
            except Exception as e:
                print(f"⚠️ Memory storage error: {e}")
        
        total_time = time.time() - total_start_time
        print(f"\n✅ Total processing time: {total_time:.2f}s")
        
        return response_text
    
    def print_stats(self):
        """Print performance statistics."""
        total_queries = self.stats['fast_path_count'] + self.stats['memory_path_count']
        if total_queries == 0:
            return
        
        fast_percentage = (self.stats['fast_path_count'] / total_queries) * 100
        
        print(f"\n📊 Performance Statistics:")
        print(f"Fast Path Queries: {self.stats['fast_path_count']} ({fast_percentage:.1f}%)")
        print(f"Memory Path Queries: {self.stats['memory_path_count']} ({100-fast_percentage:.1f}%)")
        print(f"Total Queries: {total_queries}")

async def main():
    """Main function for testing the optimized system."""
    system = OptimizedChatSystem()
    
    # Initialize system
    await system.initialize()
    
    print(f"\n{'='*60}")
    print("🚀 Optimized Adrina - LLM Self-Decision System")
    print(f"{'='*60}")
    print("⚡ Fast path for standalone queries")
    print("🧠 Memory path for contextual queries") 
    print("🤖 LLM decides which path to use")
    print(f"{'='*60}")
    print("Commands: 'quit', 'stats', 'clear'")
    print(f"{'='*60}")
    
    # Main conversation loop
    while True:
        try:
            user_input = input("\nYou: ").strip()
            
            if user_input.lower() == 'quit':
                system.print_stats()
                print("👋 Goodbye!")
                break
            elif user_input.lower() == 'stats':
                system.print_stats()
                continue
            elif user_input.lower() == 'clear':
                os.system('cls' if os.name == 'nt' else 'clear')
                continue
            elif not user_input:
                continue
            
            # Process the query
            await system.process_query(user_input)
            
        except KeyboardInterrupt:
            system.print_stats()
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            continue

if __name__ == "__main__":
    asyncio.run(main())
