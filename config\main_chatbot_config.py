# Chatbot Configuration
LLAMA_MODEL_PATH = "models\\Lexi-Llama-3-8B-Uncensored-Q8_0.gguf"
CONTEXT_WINDOW_SIZE = 8192  # Maximized for GPU acceleration (matches model's native context)
CPU_THREADS = 4  # Optimized for GPU-accelerated workload (CPU handles less work)
MAX_TOKENS = 512  # Doubled for longer, more detailed responses
TEMPERATURE = 0.7
TOP_P = 0.95
REPEAT_PENALTY = 1.1

# Performance Optimization Settings
TOP_K = 40  # Limit vocabulary for faster sampling
MIN_P = 0.05  # Minimum probability threshold for efficiency
TYPICAL_P = 1.0  # Typical sampling (1.0 = disabled for max performance)

# GPU Configuration - Optimized for maximum performance
USE_GPU = True  # Enable GPU acceleration
N_GPU_LAYERS = -1  # Use GPU for ALL available layers (auto-detect maximum)
GPU_MEMORY_FRACTION = 0.9  # Use 90% of available GPU memory for better performance
MAIN_GPU = 0  # Primary GPU device ID

# Advanced GPU Performance Settings
GPU_BATCH_SIZE = 512  # Optimized batch size for RTX 4070 Ti
USE_MMAP = True  # Memory-mapped file access for faster model loading
USE_MLOCK = True  # Lock model in RAM to prevent swapping
NUMA = False  # Disable NUMA for single-GPU setup (better performance)

# Performance Mode Settings
FAST_MODE = False  # Set to True for instant responses (disables memory & TTS)
ENABLE_MEMORY = True  # Enable/disable memory system
ENABLE_TTS = True  # Enable/disable text-to-speech

# Note: CPU_THREADS is already defined above (line 4)

# System Prompt for the chatbot
SYSTEM_PROMPT = """You are Adrina, a friendly AI assistant focused on natural conversation and helping users with questions and tasks.

IMPORTANT IDENTITY RULES:
- Your name is Adrina
- When asked about names, distinguish clearly between your name (Adrina) and the user's name
- Always check your memory for stored facts about the user before responding
- If you don't have information stored in memory, say "I don't have that information" rather than guessing

CONVERSATION GUIDELINES:
- Engage in natural, conversational responses
- Focus on being helpful, friendly, and informative
- If a user input seems unclear or like a typo, ask for clarification rather than assuming intent
- Avoid generating code unless explicitly asked for programming help
- Keep responses concise and relevant to the user's actual question
- If someone types something unclear like "cquit" or similar, treat it as a potential typo and ask what they meant

Provide accurate, helpful, and conversational responses. Be direct, informative and clear in your communication."""

# TTS Configuration
TTS_REPO_ID = "hexgrad/Kokoro-82M"
TTS_LANG_CODE = "a"
TTS_VOICE = "af_bella"
TTS_SPEED = 1
TTS_SAMPLE_RATE = 24000
TTS_FRAMES_PER_BUFFER = 512  # Smaller buffer for reduced latency and distortion

# TTS Processing Configuration - Optimized for performance
TTS_BLOCK_SIZE = 1  # Reduced for faster response (1 sentence per block)
TTS_INITIAL_BUFFER_BLOCKS = 0  # No buffering for immediate playback
TTS_MAX_WORKERS = 2  # Parallel TTS processing threads
