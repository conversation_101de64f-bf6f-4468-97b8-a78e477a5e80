#!/usr/bin/env python3
"""
Local Embedding Service for Adrina AI Assistant v2.0

Replaces Ollama dependency with local nomic-embed-text model from Hugging Face.
Provides 100% offline embedding generation for the memory system.
"""

import os
import sys
import time
import logging
import numpy as np
from typing import List, Optional, Dict, Any
from pathlib import Path

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

try:
    from transformers import AutoTokenizer, AutoModel
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: transformers not installed. Local embeddings will not work.")
    TRANSFORMERS_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

class LocalEmbeddingService:
    """
    Local embedding service using Hugging Face nomic-embed-text model.
    
    Provides 100% offline embedding generation without Ollama dependency.
    """
    
    def __init__(self, 
                 model_path: str = "models/nomic-embed-text",
                 device: str = "auto",
                 max_length: int = 512):
        """
        Initialize local embedding service.
        
        Args:
            model_path: Path to local nomic model directory
            device: Device to run on ('cpu', 'cuda', or 'auto')
            max_length: Maximum token length for embeddings
        """
        self.model_path = model_path
        self.max_length = max_length
        self.logger = logging.getLogger(__name__)
        
        # Device selection
        if device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
        
        # Model components
        self.tokenizer = None
        self.model = None
        self.is_loaded = False
        
        # Performance stats
        self.stats = {
            'total_embeddings': 0,
            'avg_processing_time': 0.0,
            'total_processing_time': 0.0
        }
        
        # Load model
        self._load_model()
    
    def _load_model(self):
        """Load the local nomic embedding model."""
        if not TRANSFORMERS_AVAILABLE:
            self.logger.error("Transformers library not available for local embeddings")
            return
        
        model_path = Path(self.model_path)
        if not model_path.exists():
            self.logger.error(f"Model not found at {model_path}")
            self.logger.info("Download with: huggingface-cli download nomic-ai/nomic-embed-text-v1 --local-dir models/nomic-embed-text")
            return
        
        try:
            self.logger.info(f"🧠 Loading local nomic model from {model_path}")
            start_time = time.time()
            
            # Load tokenizer and model
            self.tokenizer = AutoTokenizer.from_pretrained(
                str(model_path),
                local_files_only=True,
                trust_remote_code=True
            )
            
            self.model = AutoModel.from_pretrained(
                str(model_path),
                local_files_only=True,
                trust_remote_code=True
            ).to(self.device)
            
            # Set to evaluation mode
            self.model.eval()
            
            load_time = time.time() - start_time
            self.is_loaded = True
            
            self.logger.info(f"✅ Local nomic model loaded in {load_time:.2f}s on {self.device}")
            
        except Exception as e:
            self.logger.error(f"Failed to load local nomic model: {e}")
            self.is_loaded = False
    
    def generate_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for text using local nomic model.
        
        Args:
            text: Text to embed
            
        Returns:
            List of floats representing the embedding, or None if failed
        """
        if not self.is_loaded:
            self.logger.error("Local embedding model not loaded")
            return None
        
        if not text or not text.strip():
            self.logger.warning("Empty text provided for embedding")
            return None
        
        try:
            start_time = time.time()
            
            # Tokenize text
            inputs = self.tokenizer(
                text.strip(),
                max_length=self.max_length,
                truncation=True,
                padding=True,
                return_tensors="pt"
            ).to(self.device)
            
            # Generate embedding
            with torch.no_grad():
                outputs = self.model(**inputs)
                
                # Use mean pooling of last hidden states
                embeddings = outputs.last_hidden_state
                attention_mask = inputs['attention_mask']
                
                # Mean pooling
                masked_embeddings = embeddings * attention_mask.unsqueeze(-1)
                summed = torch.sum(masked_embeddings, dim=1)
                counts = torch.sum(attention_mask, dim=1, keepdim=True)
                mean_pooled = summed / counts
                
                # Normalize
                embedding = torch.nn.functional.normalize(mean_pooled, p=2, dim=1)
                
                # Convert to list
                embedding_list = embedding.cpu().numpy().flatten().tolist()
            
            processing_time = time.time() - start_time
            
            # Update stats
            self._update_stats(processing_time)
            
            self.logger.debug(f"Generated embedding of dimension {len(embedding_list)} in {processing_time:.3f}s")
            return embedding_list
            
        except Exception as e:
            self.logger.error(f"Failed to generate embedding: {e}")
            return None
    
    def generate_embeddings_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        Generate embeddings for multiple texts in batch.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embeddings (or None for failed ones)
        """
        if not self.is_loaded:
            self.logger.error("Local embedding model not loaded")
            return [None] * len(texts)
        
        try:
            start_time = time.time()
            
            # Filter empty texts
            valid_texts = [(i, text) for i, text in enumerate(texts) if text and text.strip()]
            if not valid_texts:
                return [None] * len(texts)
            
            indices, clean_texts = zip(*valid_texts)
            
            # Tokenize all texts
            inputs = self.tokenizer(
                list(clean_texts),
                max_length=self.max_length,
                truncation=True,
                padding=True,
                return_tensors="pt"
            ).to(self.device)
            
            # Generate embeddings
            with torch.no_grad():
                outputs = self.model(**inputs)
                embeddings = outputs.last_hidden_state
                attention_mask = inputs['attention_mask']
                
                # Mean pooling
                masked_embeddings = embeddings * attention_mask.unsqueeze(-1)
                summed = torch.sum(masked_embeddings, dim=1)
                counts = torch.sum(attention_mask, dim=1, keepdim=True)
                mean_pooled = summed / counts
                
                # Normalize
                normalized = torch.nn.functional.normalize(mean_pooled, p=2, dim=1)
                
                # Convert to lists
                embedding_lists = normalized.cpu().numpy().tolist()
            
            # Map back to original indices
            results = [None] * len(texts)
            for idx, embedding in zip(indices, embedding_lists):
                results[idx] = embedding
            
            processing_time = time.time() - start_time
            self._update_stats(processing_time)
            
            self.logger.debug(f"Generated {len(embedding_lists)} embeddings in {processing_time:.3f}s")
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to generate batch embeddings: {e}")
            return [None] * len(texts)
    
    def _update_stats(self, processing_time: float):
        """Update processing statistics."""
        self.stats['total_embeddings'] += 1
        self.stats['total_processing_time'] += processing_time
        self.stats['avg_processing_time'] = (
            self.stats['total_processing_time'] / self.stats['total_embeddings']
        )
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings produced by this model."""
        if not self.is_loaded:
            return 768  # Default nomic dimension
        
        try:
            # Generate a test embedding to get dimension
            test_embedding = self.generate_embedding("test")
            return len(test_embedding) if test_embedding else 768
        except:
            return 768
    
    def is_available(self) -> bool:
        """Check if the local embedding service is available."""
        return self.is_loaded
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the local model."""
        return {
            'model_path': self.model_path,
            'device': self.device,
            'is_loaded': self.is_loaded,
            'max_length': self.max_length,
            'embedding_dimension': self.get_embedding_dimension(),
            'stats': self.stats
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return self.stats.copy()

# Compatibility wrapper to replace OllamaEmbeddingService
class OfflineEmbeddingService(LocalEmbeddingService):
    """
    Drop-in replacement for OllamaEmbeddingService.
    
    Provides the same interface but uses local nomic model.
    """
    
    def __init__(self, 
                 base_url: str = None,  # Ignored for compatibility
                 model: str = "nomic-embed-text:latest",  # Ignored for compatibility
                 timeout: int = 30,  # Ignored for compatibility
                 max_retries: int = 3,  # Ignored for compatibility
                 model_path: str = "models/nomic-embed-text"):
        """Initialize with OllamaEmbeddingService-compatible interface."""
        super().__init__(model_path=model_path)
        self.logger.info("Using offline embedding service instead of Ollama")

# Test function
def test_local_embeddings():
    """Test the local embedding service."""
    print("🧪 Testing Local Embedding Service")
    print("=" * 50)
    
    service = LocalEmbeddingService()
    
    if not service.is_available():
        print("❌ Local embedding service not available")
        print("Download model with:")
        print("huggingface-cli download nomic-ai/nomic-embed-text-v1 --local-dir models/nomic-embed-text")
        return
    
    # Test single embedding
    test_text = "This is a test sentence for embedding generation."
    print(f"Testing text: '{test_text}'")
    
    embedding = service.generate_embedding(test_text)
    if embedding:
        print(f"✅ Generated embedding of dimension {len(embedding)}")
        print(f"First 5 values: {embedding[:5]}")
    else:
        print("❌ Failed to generate embedding")
    
    # Test batch embeddings
    test_texts = [
        "Hello world",
        "Machine learning is fascinating",
        "Python is a great programming language"
    ]
    
    print(f"\nTesting batch embeddings for {len(test_texts)} texts...")
    batch_embeddings = service.generate_embeddings_batch(test_texts)
    
    successful = sum(1 for emb in batch_embeddings if emb is not None)
    print(f"✅ Generated {successful}/{len(test_texts)} batch embeddings")
    
    # Show stats
    stats = service.get_stats()
    print(f"\n📊 Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n✅ Local embedding test completed!")

if __name__ == "__main__":
    test_local_embeddings()
