# connectors/chatbot_tts_memory_connector.py

"""
Enhanced <PERSON><PERSON>bot-TTS-Memory Connector for Adrina AI Assistant

This is the main connector that integrates:
- Chatbot (Llama model)
- TTS (Text-to-Speech)
- Memory System (ChromaDB + Ollama embeddings)

Provides a complete speaking AI assistant with memory capabilities.
"""

import os
import sys
import threading
import time
import queue
import re
import asyncio
from queue import Queue

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.main_chatbot_config import LLAMA_MODEL_PATH
from voice.kokoro_tts import TTSManager
from connectors.chatbot_memory_connector import ChatbotMemoryConnector

def clear_screen():
    """Clear the console screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

class OptimizedChatProcessor:
    """
    Optimized chat processor with LLM-based decision making and enhanced text cleaning.
    Decides between fast path (direct response) and memory path (context-aware response).
    """

    def __init__(self, chatbot_memory_connector):
        self.chatbot_memory_connector = chatbot_memory_connector
        self.stats = {
            'fast_path_count': 0,
            'memory_path_count': 0,
            'total_queries': 0
        }

    def split_into_sentences(self, text):
        """Split text into sentences for streaming TTS."""
        # Split on sentence endings, keeping the delimiter
        sentences = re.split(r'([.!?]+)', text)

        # Recombine sentences with their punctuation
        result = []
        for i in range(0, len(sentences) - 1, 2):
            if i + 1 < len(sentences):
                sentence = sentences[i] + sentences[i + 1]
                if sentence.strip():
                    result.append(sentence)

        # Add any remaining text
        if len(sentences) % 2 == 1 and sentences[-1].strip():
            result.append(sentences[-1])

        return result if result else [text]

    def clean_response_text(self, text):
        """Enhanced text cleaning to remove unwanted metadata and prefixes."""
        if not text:
            return text

        cleaned_text = text.strip()

        # Remove common AI response prefixes
        prefixes_to_remove = [
            "Adrina:", "Assistant:", "AI:", "Bot:", "Response:",
            "Answer:", "Reply:", "Output:", "Result:", "Chatbot:"
        ]

        for prefix in prefixes_to_remove:
            if cleaned_text.startswith(prefix):
                cleaned_text = cleaned_text[len(prefix):].strip()

        # Remove hash-tagged metadata and instruction comments
        cleaned_text = re.sub(r'\s*#[a-zA-Z0-9]+(?:#[a-zA-Z0-9]+)*\s*', ' ', cleaned_text)
        cleaned_text = re.sub(r'\s*#\d+:\d+:\d+\s*', ' ', cleaned_text)
        cleaned_text = re.sub(r'\s*Query:\s*', ' ', cleaned_text)
        cleaned_text = re.sub(r'\s*\(type your response here\)\s*', ' ', cleaned_text)
        cleaned_text = re.sub(r'\s*#\s*Provide a response.*?$', '', cleaned_text, flags=re.IGNORECASE | re.DOTALL)
        cleaned_text = re.sub(r'\s*#\s*Remember to maintain.*?$', '', cleaned_text, flags=re.IGNORECASE | re.DOTALL)
        cleaned_text = re.sub(r'\s*Answer in a conversational tone.*?$', '', cleaned_text, flags=re.IGNORECASE | re.DOTALL)

        # Remove validation prompts and meta-commentary
        cleaned_text = re.sub(r'\.\s*Adrina\'s responses are.*?YES or NO:\s*YES\s*YES.*$', '.', cleaned_text, flags=re.IGNORECASE | re.DOTALL)
        cleaned_text = re.sub(r'\.\s*Do you agree with this response\?.*?YES.*$', '.', cleaned_text, flags=re.IGNORECASE | re.DOTALL)
        cleaned_text = re.sub(r'\.\s*In this case.*?YES.*$', '.', cleaned_text, flags=re.IGNORECASE | re.DOTALL)

        # Remove standalone "Adrina" at the end
        cleaned_text = re.sub(r'\n\s*Adrina\s*$', '', cleaned_text, flags=re.IGNORECASE)
        cleaned_text = re.sub(r'\s+Adrina\s*$', '', cleaned_text, flags=re.IGNORECASE)

        # Clean up extra whitespace
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

        return cleaned_text

    async def decide_processing_path(self, user_input):
        """Fast decision making with pattern matching + LLM fallback."""
        start_time = time.time()
        user_lower = user_input.lower().strip()

        # Fast pattern matching for obvious cases
        fast_patterns = [
            'tell me a joke', 'joke', 'funny', 'laugh',
            'hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening',
            'how are you', 'what\'s up', 'sup',
            'explain', 'what is', 'how to', 'how do',
            'define', 'meaning of', 'weather', 'time',
            'calculate', 'math', 'solve'
        ]

        memory_patterns = [
            'my name', 'who am i', 'what do you know about me',
            'remember', 'recall', 'what did i', 'what did we',
            'my favorite', 'i like', 'i prefer', 'preference',
            'last time', 'before', 'earlier', 'previous'
        ]

        # Check for obvious FAST path patterns
        for pattern in fast_patterns:
            if pattern in user_lower:
                decision_time = time.time() - start_time
                print(f"🚀 Fast Decision: FAST path for '{user_input[:30]}...'")
                print(f"🚀 Decision made in {decision_time:.3f}s")
                return False

        # Check for obvious MEMORY path patterns
        for pattern in memory_patterns:
            if pattern in user_lower:
                decision_time = time.time() - start_time
                print(f"🧠 Fast Decision: MEMORY path for '{user_input[:30]}...'")
                print(f"🧠 Decision made in {decision_time:.3f}s")
                return True

        # For ambiguous cases, use LLM (but with shorter prompt)
        decision_prompt = f"Query: '{user_input}'\nNeeds personal context/memory? Answer: FAST or MEMORY"

        try:
            decision_response = ""
            # Generate decision using streaming (but stop very early)
            for chunk in self.chatbot_memory_connector.chatbot.generate_response_stream(decision_prompt):
                decision_response += chunk
                if len(decision_response) > 20:  # Stop very early
                    break

            decision_time = time.time() - start_time

            # Extract decision
            decision_response = decision_response.strip().upper()
            needs_memory = "MEMORY" in decision_response

            path_type = "MEMORY" if needs_memory else "FAST"
            print(f"🤖 LLM Decision: {path_type} path for '{user_input[:30]}...'")
            print(f"🤖 Decision made in {decision_time:.3f}s")

            return needs_memory

        except Exception as e:
            decision_time = time.time() - start_time
            print(f"⚠️ Decision error: {e}, defaulting to FAST path")
            print(f"⚠️ Decision made in {decision_time:.3f}s")
            return False  # Default to FAST path for better performance

class EnhancedGeneratorThread(threading.Thread):
    """Enhanced generator thread with memory context and prefix stripping."""
    
    def __init__(self, chatbot_memory_connector, user_input, sentence_queue, use_memory=True):
        super().__init__()
        self.chatbot_memory_connector = chatbot_memory_connector
        self.user_input = user_input
        self.sentence_queue = sentence_queue
        self.use_memory = use_memory
        self._stop_event = threading.Event()
        
        # Prefixes to strip from AI responses
        self.prefixes_to_strip = [
            "Adrina:", "Assistant:", "AI:", "Bot:", "Response:", 
            "Answer:", "Reply:", "Output:", "Result:"
        ]
    
    def run(self):
        """Generate response with memory context and queue sentences for TTS."""
        try:
            # Get memory context
            context = ""
            if self.chatbot_memory_connector.is_memory_enabled():
                # Get conversation context
                conversation_context = self.chatbot_memory_connector.memory_connector.get_conversation_context(self.user_input)

                # Get relevant facts and preferences with multiple search strategies
                relevant_memories = []

                # Strategy 1: Direct query search
                direct_memories = self.chatbot_memory_connector.memory_connector.memory_manager.retrieve_relevant_memories(
                    query_text=self.user_input,
                    memory_types=["fact", "preference"],
                    max_results=5
                )
                relevant_memories.extend(direct_memories)

                # Strategy 2: Enhanced search for name-related queries
                if any(term in self.user_input.lower() for term in ["my name", "what is my name", "who am i", "name is"]):
                    name_search_terms = ["user name", "name is", "called", "Anirban Das", "user identification"]
                    for term in name_search_terms:
                        term_memories = self.chatbot_memory_connector.memory_connector.memory_manager.retrieve_relevant_memories(
                            query_text=term,
                            memory_types=["fact", "preference"],
                            max_results=3
                        )
                        relevant_memories.extend(term_memories)

                # Remove duplicates while preserving order
                seen_texts = set()
                unique_memories = []
                for memory in relevant_memories:
                    text = memory["text"]
                    if text not in seen_texts:
                        seen_texts.add(text)
                        unique_memories.append(memory)

                relevant_memories = unique_memories[:10]  # Limit to top 10

                # Format all context
                context_parts = []

                # Add relevant facts and preferences first
                if relevant_memories:
                    context_parts.append("Relevant Information:")
                    for memory in relevant_memories:
                        memory_type = memory["metadata"].get("memory_type", "unknown")
                        text = memory["text"]
                        context_parts.append(f"- [{memory_type.title()}] {text}")
                    context_parts.append("")  # Empty line

                # Add conversation context
                if conversation_context:
                    context_parts.append("Recent Conversation:")
                    context_parts.append(conversation_context)

                context = "\n".join(context_parts)

            # Prepare enhanced prompt
            enhanced_input = self.user_input
            if context:
                enhanced_input = f"""Memory Context:
{context}

Current User Input: {self.user_input}

Please respond considering the memory context above and maintaining conversation continuity. Use the relevant information to provide accurate and personalized responses."""
            
            # Generate streaming response
            full_response = ""
            current_sentence = ""
            first_chunk = True
            
            for chunk in self.chatbot_memory_connector.chatbot.generate_response_stream(enhanced_input):
                if self._stop_event.is_set():
                    break
                
                # Strip prefixes from the first chunk
                if first_chunk:
                    chunk = self._strip_prefixes(chunk)
                    first_chunk = False
                
                print(chunk, end="", flush=True)
                full_response += chunk
                current_sentence += chunk
                
                # Check for sentence boundaries
                if any(punct in chunk for punct in ['.', '!', '?', '\n']):
                    if current_sentence.strip():
                        clean_sentence = current_sentence.strip()
                        if clean_sentence and len(clean_sentence) > 3:  # Minimum sentence length
                            self.sentence_queue.put(clean_sentence)
                        current_sentence = ""

                # Also queue sentences when we have enough text (for longer responses)
                elif len(current_sentence) > 100:
                    # Look for natural break points
                    if any(break_point in chunk for break_point in [',', ';', ':']):
                        if current_sentence.strip():
                            clean_sentence = current_sentence.strip()
                            if clean_sentence and len(clean_sentence) > 10:
                                self.sentence_queue.put(clean_sentence)
                            current_sentence = ""
            
            # Add any remaining text as a sentence
            if current_sentence.strip():
                clean_sentence = current_sentence.strip()
                if len(clean_sentence) > 3:
                    self.sentence_queue.put(clean_sentence)

            # Signal end of generation
            self.sentence_queue.put(None)
            
            # Store conversation in memory
            if self.chatbot_memory_connector.is_memory_enabled():
                self.chatbot_memory_connector.memory_connector.store_conversation(self.user_input, full_response)
            
        except Exception as e:
            print(f"\nError in generation: {e}")
            self.sentence_queue.put(None)
    
    def _strip_prefixes(self, text):
        """Strip common AI response prefixes."""
        text = text.strip()

        # Keep stripping until no more prefixes are found
        changed = True
        while changed:
            changed = False
            for prefix in self.prefixes_to_strip:
                if text.startswith(prefix):
                    text = text[len(prefix):].strip()
                    changed = True
                    break

        return text
    
    def stop(self):
        """Stop the generation thread."""
        self._stop_event.set()

class SpeechThread(threading.Thread):
    """Enhanced thread for handling TTS speech synthesis with real-time filtering."""

    def __init__(self, sentence_queue, tts_manager, chat_processor):
        super().__init__()
        self.sentence_queue = sentence_queue
        self.tts_manager = tts_manager
        self.chat_processor = chat_processor
        self._stop_event = threading.Event()
        self.sentence_buffer = ""

    def run(self):
        """Process sentences from queue and convert to speech with real-time filtering."""
        while not self._stop_event.is_set():
            try:
                chunk = self.sentence_queue.get(timeout=0.1)

                if chunk is None:  # End signal
                    # Process any remaining text in buffer
                    if self.sentence_buffer.strip():
                        final_chunk = self._clean_text_chunk(self.sentence_buffer.strip())
                        if final_chunk.strip():
                            print(f"🎤 TTS: Processing final chunk: '{final_chunk.strip()}'")
                            self.tts_manager.speak(final_chunk.strip())
                    break

                self.sentence_buffer += chunk

                # Filter out hash-tagged metadata and instruction comments from the buffer
                self.sentence_buffer = re.sub(r'\s*#[a-zA-Z0-9]+(?:#[a-zA-Z0-9]+)*\s*', ' ', self.sentence_buffer)
                self.sentence_buffer = re.sub(r'\s*#\d+:\d+:\d+\s*', ' ', self.sentence_buffer)
                self.sentence_buffer = re.sub(r'\s*Query:\s*', ' ', self.sentence_buffer)
                self.sentence_buffer = re.sub(r'\s*#\s*Provide a response.*?$', '', self.sentence_buffer, flags=re.IGNORECASE | re.DOTALL)
                self.sentence_buffer = re.sub(r'\s*#\s*Remember to maintain.*?$', '', self.sentence_buffer, flags=re.IGNORECASE | re.DOTALL)

                # Check if we have complete sentences to process
                sentences = self.chat_processor.split_into_sentences(self.sentence_buffer)

                if len(sentences) > 1:  # We have at least one complete sentence
                    # Process all complete sentences except the last (incomplete) one
                    for sentence in sentences[:-1]:
                        if sentence.strip():
                            clean_sentence = self._clean_text_chunk(sentence.strip())
                            if clean_sentence.strip():
                                print(f"🎤 TTS: Processing chunk: '{clean_sentence.strip()}'")
                                self.tts_manager.speak(clean_sentence.strip())

                    # Keep the last (potentially incomplete) sentence in buffer
                    self.sentence_buffer = sentences[-1]

            except queue.Empty:
                # Timeout waiting for chunk - this is normal, just continue
                continue
            except Exception as e:
                if not self._stop_event.is_set():
                    print(f"\nTTS Error: {e}")
                break

    def _clean_text_chunk(self, text):
        """Clean text chunk for TTS processing."""
        if not text:
            return text

        # Use the chat processor's cleaning method
        return self.chat_processor.clean_response_text(text)

    def stop(self):
        """Stop the speech thread."""
        self._stop_event.set()

async def main_async():
    """Main async function for the enhanced Adrina assistant."""
    # Check if model exists
    if not os.path.exists(LLAMA_MODEL_PATH):
        print(f"Error: Model file '{LLAMA_MODEL_PATH}' not found!")
        return

    print("Initializing enhanced Adrina with memory...")
    
    # Initialize enhanced chatbot with memory
    try:
        chatbot_memory = ChatbotMemoryConnector(LLAMA_MODEL_PATH)
        print("✅ Enhanced chatbot initialized")
    except Exception as e:
        print(f"❌ Failed to initialize chatbot: {e}")
        return
    
    # Initialize TTS
    try:
        tts_manager = TTSManager()
        print("✅ TTS system initialized")
    except Exception as e:
        print(f"❌ Failed to initialize TTS: {e}")
        return

    # Initialize optimized chat processor
    try:
        chat_processor = OptimizedChatProcessor(chatbot_memory)
        print("✅ Optimized chat processor initialized")
    except Exception as e:
        print(f"❌ Failed to initialize chat processor: {e}")
        return

    # Start memory session
    session_id = chatbot_memory.start_session()
    
    clear_screen()
    
    print("=" * 60)
    print("🎤 Welcome to Enhanced Adrina - AI Assistant with Memory!")
    print("=" * 60)
    
    # Display system status
    status = chatbot_memory.get_status()
    memory_enabled = status["memory_status"]["memory_enabled"]
    
    print(f"🧠 Memory System: {'✅ ENABLED' if memory_enabled else '❌ DISABLED'}")
    print(f"🎯 Session ID: {session_id}")
    
    if memory_enabled:
        print("\n💡 Memory Commands:")
        print("  'memory' - Show memory statistics")
        print("  'remember <fact>' - Store a fact")
        print("  'prefer <preference>' - Store a preference")
        print("  'search <query>' - Search memories")
    
    print("\n🎮 Controls:")
    print("  'quit' - Exit application")
    print("  'clear' - Clear screen")
    print("=" * 60 + "\n")

    try:
        while True:
            user_input = input("You: ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'clear':
                clear_screen()
                continue
            elif user_input.lower() == 'memory':
                if memory_enabled:
                    stats = chatbot_memory.get_memory_stats()
                    print(f"\n📊 Memory Statistics:")
                    print(f"   Total memories: {stats.get('total_memories', 0)}")
                    print(f"   Memory types: {stats.get('memory_types', {})}")
                    print(f"   Session conversations: {stats.get('conversation_count', 0)}")
                    print(f"   Embedding service: {'✅' if stats.get('embedding_service_available') else '❌'}")
                    print()
                else:
                    print("\n🧠 Memory system is disabled\n")
                continue
            elif user_input.lower().startswith('remember '):
                if memory_enabled:
                    fact = user_input[9:]  # Remove 'remember ' prefix
                    success = chatbot_memory.store_fact(fact, category="user_input")
                    print(f"{'✅ Stored in memory' if success else '❌ Failed to store'}\n")
                else:
                    print("🧠 Memory system is disabled\n")
                continue
            elif user_input.lower().startswith('prefer '):
                if memory_enabled:
                    preference = user_input[7:]  # Remove 'prefer ' prefix
                    success = chatbot_memory.store_preference(preference)
                    print(f"{'✅ Preference stored' if success else '❌ Failed to store preference'}\n")
                else:
                    print("🧠 Memory system is disabled\n")
                continue
            elif user_input.lower().startswith('search '):
                if memory_enabled:
                    query = user_input[7:]  # Remove 'search ' prefix
                    results = chatbot_memory.search_memories(query, max_results=3)
                    print(f"\n🔍 Search Results for '{query}':")
                    if results:
                        for i, result in enumerate(results, 1):
                            memory_type = result["metadata"].get("memory_type", "unknown")
                            score = result["similarity_score"]
                            text = result["text"][:100] + "..." if len(result["text"]) > 100 else result["text"]
                            print(f"   {i}. [{memory_type.title()}] (Score: {score:.3f}) {text}")
                    else:
                        print("   No matching memories found")
                    print()
                else:
                    print("🧠 Memory system is disabled\n")
                continue
            elif not user_input:
                continue

            print(f"\n{'='*60}")
            print(f"Processing: {user_input}")
            print(f"{'='*60}")

            # Step 1: LLM decides processing path
            start_time = time.time()
            needs_memory = await chat_processor.decide_processing_path(user_input)
            chat_processor.stats['total_queries'] += 1

            print("\nAdrina: ", end="", flush=True)

            # Step 2: Generate response with streaming TTS
            response_start = time.time()

            # Create queue for streaming text to TTS
            text_queue = Queue()

            # Start TTS streaming with enhanced speech thread
            speech_thread = SpeechThread(text_queue, tts_manager, chat_processor)
            speech_thread.start()

            # Start generation with appropriate path
            generator = EnhancedGeneratorThread(chatbot_memory, user_input, text_queue, use_memory=needs_memory)
            generator.start()

            # Wait for generation to complete
            generator.join()

            # Wait for TTS to complete
            speech_thread.join(timeout=10)  # 10 second timeout
            if speech_thread.is_alive():
                speech_thread.stop()

            response_time = time.time() - response_start
            total_time = time.time() - start_time

            # Update stats
            if needs_memory:
                chat_processor.stats['memory_path_count'] += 1
            else:
                chat_processor.stats['fast_path_count'] += 1

            print(f"\n🔊 Streaming TTS completed")
            print(f"\n✅ Total processing time: {total_time:.2f}s")
            print()  # Extra line for readability
            
    except (KeyboardInterrupt, EOFError):
        print("\n\nGoodbye! 👋")
    finally:
        print("Shutting down enhanced Adrina...")
        
        # Display final memory stats
        if memory_enabled:
            try:
                final_stats = chatbot_memory.get_memory_stats()
                print(f"Final session stats: {final_stats.get('conversation_count', 0)} conversations stored")
            except:
                pass

        # Display processing stats
        try:
            print(f"Processing stats: {chat_processor.stats['fast_path_count']} fast, {chat_processor.stats['memory_path_count']} memory, {chat_processor.stats['total_queries']} total")
        except:
            pass

def main():
    """Wrapper function to run the async main."""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()
